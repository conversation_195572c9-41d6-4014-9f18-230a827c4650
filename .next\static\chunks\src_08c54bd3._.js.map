{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\n// Format date\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\n// Generate slug from string\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n}\n\n// Calculate discount percentage\nexport function calculateDiscountPercentage(originalPrice: number, salePrice: number): number {\n  if (originalPrice <= 0) return 0\n  return Math.round(((originalPrice - salePrice) / originalPrice) * 100)\n}\n\n// Truncate text\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Generate random ID\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone number (basic)\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/\n  return phoneRegex.test(phone)\n}\n\n// Get image URL with fallback\nexport function getImageUrl(url?: string, fallback: string = '/placeholder-image.jpg'): string {\n  return url || fallback\n}\n\n// Calculate cart total\nexport function calculateCartTotal(items: Array<{ price: number; quantity: number }>): number {\n  return items.reduce((total, item) => total + (item.price * item.quantity), 0)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Check if object is empty\nexport function isEmpty(obj: any): boolean {\n  if (obj == null) return true\n  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0\n  return Object.keys(obj).length === 0\n}\n\n// Deep clone object\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') return obj\n  if (obj instanceof Date) return new Date(obj.getTime()) as any\n  if (Array.isArray(obj)) return obj.map(item => deepClone(item)) as any\n  \n  const cloned = {} as T\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      cloned[key] = deepClone(obj[key])\n    }\n  }\n  return cloned\n}\n\n// Get initials from name\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\n// Generate order number\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase()\n  return `ORD-${timestamp}-${random}`\n}\n\n// Parse search params\nexport function parseSearchParams(searchParams: URLSearchParams) {\n  const params: Record<string, any> = {}\n  \n  for (const [key, value] of searchParams.entries()) {\n    if (key.endsWith('[]')) {\n      const arrayKey = key.slice(0, -2)\n      if (!params[arrayKey]) params[arrayKey] = []\n      params[arrayKey].push(value)\n    } else {\n      params[key] = value\n    }\n  }\n  \n  return params\n}\n\n// Build search params\nexport function buildSearchParams(params: Record<string, any>): string {\n  const searchParams = new URLSearchParams()\n  \n  for (const [key, value] of Object.entries(params)) {\n    if (value === undefined || value === null || value === '') continue\n    \n    if (Array.isArray(value)) {\n      value.forEach(item => searchParams.append(`${key}[]`, item.toString()))\n    } else {\n      searchParams.append(key, value.toString())\n    }\n  }\n  \n  return searchParams.toString()\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAGO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAmB;IAChE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAGO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,IAAI,kCAAkC;;AAC/D;AAGO,SAAS,4BAA4B,aAAqB,EAAE,SAAiB;IAClF,IAAI,iBAAiB,GAAG,OAAO;IAC/B,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,SAAS,IAAI,gBAAiB;AACpE;AAGO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,YAAY,GAAY;QAAE,WAAA,iEAAmB;IAC3D,OAAO,OAAO;AAChB;AAGO,SAAS,mBAAmB,KAAiD;IAClF,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;AAC7E;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,QAAQ,GAAQ;IAC9B,IAAI,OAAO,MAAM,OAAO;IACxB,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,QAAQ,UAAU,OAAO,IAAI,MAAM,KAAK;IACzE,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK;AACrC;AAGO,SAAS,UAAa,GAAM;IACjC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;IACpD,IAAI,eAAe,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO;IACpD,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,UAAU;IAEzD,MAAM,SAAS,CAAC;IAChB,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,IAAI,cAAc,CAAC,MAAM;YAC3B,MAAM,CAAC,IAAI,GAAG,UAAU,GAAG,CAAC,IAAI;QAClC;IACF;IACA,OAAO;AACT;AAGO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAGO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,AAAC,OAAmB,OAAb,WAAU,KAAU,OAAP;AAC7B;AAGO,SAAS,kBAAkB,YAA6B;IAC7D,MAAM,SAA8B,CAAC;IAErC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAa,OAAO,GAAI;QACjD,IAAI,IAAI,QAAQ,CAAC,OAAO;YACtB,MAAM,WAAW,IAAI,KAAK,CAAC,GAAG,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE;YAC5C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACxB,OAAO;YACL,MAAM,CAAC,IAAI,GAAG;QAChB;IACF;IAEA,OAAO;AACT;AAGO,SAAS,kBAAkB,MAA2B;IAC3D,MAAM,eAAe,IAAI;IAEzB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;QAE3D,IAAI,MAAM,OAAO,CAAC,QAAQ;YACxB,MAAM,OAAO,CAAC,CAAA,OAAQ,aAAa,MAAM,CAAC,AAAC,GAAM,OAAJ,KAAI,OAAK,KAAK,QAAQ;QACrE,OAAO;YACL,aAAa,MAAM,CAAC,KAAK,MAAM,QAAQ;QACzC;IACF;IAEA,OAAO,aAAa,QAAQ;AAC9B", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/products/ProductCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline'\nimport { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid'\nimport { Product } from '@/types/database'\nimport { formatCurrency, calculateDiscountPercentage, getImageUrl } from '@/lib/utils'\nimport { cn } from '@/lib/utils'\n\ninterface ProductCardProps {\n  product: Product\n  className?: string\n  showQuickAdd?: boolean\n}\n\nexport default function ProductCard({ \n  product, \n  className,\n  showQuickAdd = true \n}: ProductCardProps) {\n  const [isWishlisted, setIsWishlisted] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n\n  const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0]\n  const hasDiscount = product.compare_price && product.compare_price > product.price\n  const discountPercentage = hasDiscount \n    ? calculateDiscountPercentage(product.compare_price!, product.price)\n    : 0\n\n  const handleWishlistToggle = async (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    \n    setIsWishlisted(!isWishlisted)\n    // TODO: Implement wishlist API call\n  }\n\n  const handleQuickAdd = async (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    \n    setIsLoading(true)\n    try {\n      // TODO: Implement add to cart API call\n      console.log('Adding to cart:', product.id)\n    } catch (error) {\n      console.error('Error adding to cart:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className={cn(\n      \"group relative bg-background border border-border rounded-xl overflow-hidden hover-lift hover-glow transition-all duration-500 animate-scale-in\",\n      className\n    )}>\n      <Link href={`/products/${product.slug}`}>\n        {/* Product Image */}\n        <div className=\"relative aspect-square overflow-hidden bg-gradient-to-br from-green-50 to-green-100\">\n          {primaryImage ? (\n            <Image\n              src={getImageUrl(primaryImage.url)}\n              alt={primaryImage.alt_text || product.name}\n              fill\n              className=\"object-cover group-hover:scale-110 transition-transform duration-500 ease-out\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n          ) : (\n            <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100\">\n              <div className=\"text-center animate-float\">\n                <div className=\"text-4xl mb-2\">🍎</div>\n                <span className=\"text-muted-foreground text-sm\">Fresh Product</span>\n              </div>\n            </div>\n          )}\n\n          {/* Discount Badge */}\n          {hasDiscount && (\n            <div className=\"absolute top-3 left-3 gradient-berry text-white text-xs font-bold px-3 py-1.5 rounded-full animate-bounce-gentle shadow-lg\">\n              <span className=\"flex items-center space-x-1\">\n                <span>🔥</span>\n                <span>-{discountPercentage}%</span>\n              </span>\n            </div>\n          )}\n\n          {/* Featured Badge */}\n          {product.is_featured && (\n            <div className=\"absolute top-3 right-3 gradient-tropical text-white text-xs font-bold px-3 py-1.5 rounded-full animate-pulse-slow shadow-lg\">\n              <span className=\"flex items-center space-x-1\">\n                <span>⭐</span>\n                <span>Featured</span>\n              </span>\n            </div>\n          )}\n\n          {/* Fresh Badge */}\n          <div className=\"absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm text-green-700 text-xs font-medium px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n            <span className=\"flex items-center space-x-1\">\n              <span>🌱</span>\n              <span>Fresh</span>\n            </span>\n          </div>\n\n          {/* Wishlist Button */}\n          <button\n            onClick={handleWishlistToggle}\n            className=\"absolute top-2 right-2 p-2 bg-background/80 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background\"\n            aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}\n          >\n            {isWishlisted ? (\n              <HeartIconSolid className=\"h-4 w-4 text-destructive\" />\n            ) : (\n              <HeartIcon className=\"h-4 w-4 text-muted-foreground hover:text-destructive\" />\n            )}\n          </button>\n\n          {/* Quick Add Button */}\n          {showQuickAdd && (\n            <button\n              onClick={handleQuickAdd}\n              disabled={isLoading || product.quantity <= 0}\n              className=\"absolute bottom-3 left-3 right-3 gradient-fresh text-white py-3 px-4 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover-scale disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-lg font-medium\"\n            >\n              <ShoppingBagIcon className=\"h-4 w-4\" />\n              <span className=\"text-sm\">\n                {isLoading ? (\n                  <span className=\"flex items-center space-x-1\">\n                    <span className=\"animate-spin\">⏳</span>\n                    <span>Adding...</span>\n                  </span>\n                ) : product.quantity <= 0 ? (\n                  <span className=\"flex items-center space-x-1\">\n                    <span>❌</span>\n                    <span>Out of Stock</span>\n                  </span>\n                ) : (\n                  <span className=\"flex items-center space-x-1\">\n                    <span>🛒</span>\n                    <span>Add to Cart</span>\n                  </span>\n                )}\n              </span>\n            </button>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div className=\"p-5 space-y-3\">\n          {/* Brand */}\n          {product.brand && (\n            <div className=\"flex items-center space-x-1\">\n              <span className=\"text-xs\">🏷️</span>\n              <p className=\"text-xs text-muted-foreground uppercase tracking-wide font-medium\">\n                {product.brand}\n              </p>\n            </div>\n          )}\n\n          {/* Product Name */}\n          <h3 className=\"font-semibold text-foreground line-clamp-2 group-hover:text-primary transition-colors duration-300 text-lg\">\n            {product.name}\n          </h3>\n\n          {/* Short Description */}\n          {product.short_description && (\n            <p className=\"text-sm text-muted-foreground line-clamp-2 leading-relaxed\">\n              {product.short_description}\n            </p>\n          )}\n\n          {/* Price */}\n          <div className=\"flex items-center space-x-3\">\n            <span className=\"text-xl font-bold text-primary\">\n              {formatCurrency(product.price)}\n            </span>\n            {hasDiscount && (\n              <span className=\"text-sm text-muted-foreground line-through bg-red-50 px-2 py-1 rounded\">\n                {formatCurrency(product.compare_price!)}\n              </span>\n            )}\n          </div>\n\n          {/* Stock Status & Rating */}\n          <div className=\"flex items-center justify-between pt-2 border-t border-border/50\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={cn(\n                \"w-3 h-3 rounded-full animate-pulse-slow\",\n                product.quantity > 10 ? \"bg-green-500\" :\n                product.quantity > 0 ? \"bg-yellow-500\" : \"bg-red-500\"\n              )} />\n              <span className=\"text-xs font-medium\">\n                {product.quantity > 10 ? (\n                  <span className=\"text-green-600 flex items-center space-x-1\">\n                    <span>✅</span>\n                    <span>Fresh & Available</span>\n                  </span>\n                ) : product.quantity > 0 ? (\n                  <span className=\"text-yellow-600 flex items-center space-x-1\">\n                    <span>⚡</span>\n                    <span>Only {product.quantity} left</span>\n                  </span>\n                ) : (\n                  <span className=\"text-red-600 flex items-center space-x-1\">\n                    <span>❌</span>\n                    <span>Out of Stock</span>\n                  </span>\n                )}\n              </span>\n            </div>\n\n            {/* Rating */}\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"flex space-x-0.5\">\n                {[...Array(5)].map((_, i) => (\n                  <svg\n                    key={i}\n                    className=\"w-3.5 h-3.5 text-yellow-400 fill-current hover-scale\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path d=\"M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z\" />\n                  </svg>\n                ))}\n              </div>\n              <span className=\"text-xs text-muted-foreground font-medium\">(4.8)</span>\n            </div>\n          </div>\n        </div>\n      </Link>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAEA;;;AARA;;;;;;;;AAiBe,SAAS,YAAY,KAIjB;QAJiB,EAClC,OAAO,EACP,SAAS,EACT,eAAe,IAAI,EACF,GAJiB;QAQb,iBAA+C;;IAHpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAAC;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,MAAM,eAAe,EAAA,kBAAA,QAAQ,MAAM,cAAd,sCAAA,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,UAAU,QAAK,mBAAA,QAAQ,MAAM,cAAd,uCAAA,gBAAgB,CAAC,EAAE;IACvF,MAAM,cAAc,QAAQ,aAAa,IAAI,QAAQ,aAAa,GAAG,QAAQ,KAAK;IAClF,MAAM,qBAAqB,cACvB,IAAA,qJAA2B,EAAC,QAAQ,aAAa,EAAG,QAAQ,KAAK,IACjE;IAEJ,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,gBAAgB,CAAC;IACjB,oCAAoC;IACtC;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,aAAa;QACb,IAAI;YACF,uCAAuC;YACvC,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAChB,mJACA;kBAEA,cAAA,6LAAC,0KAAI;YAAC,MAAM,AAAC,aAAyB,OAAb,QAAQ,IAAI;;8BAEnC,6LAAC;oBAAI,WAAU;;wBACZ,6BACC,6LAAC,2IAAK;4BACJ,KAAK,IAAA,qIAAW,EAAC,aAAa,GAAG;4BACjC,KAAK,aAAa,QAAQ,IAAI,QAAQ,IAAI;4BAC1C,IAAI;4BACJ,WAAU;4BACV,OAAM;;;;;iDAGR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;wBAMrD,6BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAK;4CAAE;4CAAmB;;;;;;;;;;;;;;;;;;wBAMhC,QAAQ,WAAW,kBAClB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAY,eAAe,yBAAyB;sCAEnD,6BACC,6LAAC,+NAAc;gCAAC,WAAU;;;;;qDAE1B,6LAAC,iOAAS;gCAAC,WAAU;;;;;;;;;;;wBAKxB,8BACC,6LAAC;4BACC,SAAS;4BACT,UAAU,aAAa,QAAQ,QAAQ,IAAI;4BAC3C,WAAU;;8CAEV,6LAAC,mPAAe;oCAAC,WAAU;;;;;;8CAC3B,6LAAC;oCAAK,WAAU;8CACb,0BACC,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,6LAAC;0DAAK;;;;;;;;;;;+CAEN,QAAQ,QAAQ,IAAI,kBACtB,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;6DAGR,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlB,6LAAC;oBAAI,WAAU;;wBAEZ,QAAQ,KAAK,kBACZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAE,WAAU;8CACV,QAAQ,KAAK;;;;;;;;;;;;sCAMpB,6LAAC;4BAAG,WAAU;sCACX,QAAQ,IAAI;;;;;;wBAId,QAAQ,iBAAiB,kBACxB,6LAAC;4BAAE,WAAU;sCACV,QAAQ,iBAAiB;;;;;;sCAK9B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,IAAA,wIAAc,EAAC,QAAQ,KAAK;;;;;;gCAE9B,6BACC,6LAAC;oCAAK,WAAU;8CACb,IAAA,wIAAc,EAAC,QAAQ,aAAa;;;;;;;;;;;;sCAM3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,IAAA,4HAAE,EAChB,2CACA,QAAQ,QAAQ,GAAG,KAAK,iBACxB,QAAQ,QAAQ,GAAG,IAAI,kBAAkB;;;;;;sDAE3C,6LAAC;4CAAK,WAAU;sDACb,QAAQ,QAAQ,GAAG,mBAClB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;uDAEN,QAAQ,QAAQ,GAAG,kBACrB,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;kEAAK;;;;;;kEACN,6LAAC;;4DAAK;4DAAM,QAAQ,QAAQ;4DAAC;;;;;;;;;;;;qEAG/B,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;kEAAK;;;;;;kEACN,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;8CAOd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oDAEC,WAAU;oDACV,SAAQ;8DAER,cAAA,6LAAC;wDAAK,GAAE;;;;;;mDAJH;;;;;;;;;;sDAQX,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;GAzNwB;KAAA", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/products/ProductGrid.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Product, PaginatedResponse, ProductFilters, ProductSort } from '@/types/database'\nimport ProductCard from './ProductCard'\nimport { cn } from '@/lib/utils'\n\ninterface ProductGridProps {\n  initialProducts?: PaginatedResponse<Product>\n  filters?: ProductFilters\n  sort?: ProductSort\n  className?: string\n  showFilters?: boolean\n  showSort?: boolean\n}\n\nexport default function ProductGrid({\n  initialProducts,\n  filters = {},\n  sort = { field: 'created_at', direction: 'desc' },\n  className,\n  showFilters = true,\n  showSort = true\n}: ProductGridProps) {\n  const [products, setProducts] = useState<PaginatedResponse<Product> | null>(initialProducts || null)\n  const [loading, setLoading] = useState(!initialProducts)\n  const [currentFilters, setCurrentFilters] = useState<ProductFilters>(filters)\n  const [currentSort, setCurrentSort] = useState<ProductSort>(sort)\n\n  useEffect(() => {\n    fetchProducts()\n  }, [currentFilters, currentSort])\n\n  const fetchProducts = async (page = 1) => {\n    setLoading(true)\n    try {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: '12',\n        sortField: currentSort.field,\n        sortDirection: currentSort.direction,\n        ...Object.fromEntries(\n          Object.entries(currentFilters).filter(([_, value]) => \n            value !== undefined && value !== null && value !== ''\n          )\n        )\n      })\n\n      const response = await fetch(`/api/products?${params}`)\n      if (!response.ok) throw new Error('Failed to fetch products')\n      \n      const data = await response.json()\n      setProducts(data)\n    } catch (error) {\n      console.error('Error fetching products:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {\n    setCurrentFilters(prev => ({ ...prev, ...newFilters }))\n  }\n\n  const handleSortChange = (newSort: ProductSort) => {\n    setCurrentSort(newSort)\n  }\n\n  const handlePageChange = (page: number) => {\n    fetchProducts(page)\n  }\n\n  if (loading && !products) {\n    return (\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {[...Array(12)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"aspect-square bg-muted rounded-lg mb-4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-muted rounded w-3/4\"></div>\n              <div className=\"h-4 bg-muted rounded w-1/2\"></div>\n              <div className=\"h-6 bg-muted rounded w-1/3\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (!products || products.data.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"max-w-md mx-auto\">\n          <div className=\"w-24 h-24 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center\">\n            <svg className=\"w-12 h-12 text-muted-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium text-foreground mb-2\">No products found</h3>\n          <p className=\"text-muted-foreground\">\n            Try adjusting your search criteria or browse our categories to find what you're looking for.\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={cn(\"space-y-6\", className)}>\n      {/* Filters and Sort */}\n      {(showFilters || showSort) && (\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 border-b pb-4\">\n          {/* Results count */}\n          <div className=\"text-sm text-muted-foreground\">\n            Showing {products.data.length} of {products.pagination.total} products\n          </div>\n\n          {/* Sort dropdown */}\n          {showSort && (\n            <div className=\"flex items-center space-x-2\">\n              <label htmlFor=\"sort\" className=\"text-sm font-medium text-foreground\">\n                Sort by:\n              </label>\n              <select\n                id=\"sort\"\n                value={`${currentSort.field}-${currentSort.direction}`}\n                onChange={(e) => {\n                  const [field, direction] = e.target.value.split('-') as [ProductSort['field'], ProductSort['direction']]\n                  handleSortChange({ field, direction })\n                }}\n                className=\"px-3 py-1 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"created_at-desc\">Newest First</option>\n                <option value=\"created_at-asc\">Oldest First</option>\n                <option value=\"name-asc\">Name A-Z</option>\n                <option value=\"name-desc\">Name Z-A</option>\n                <option value=\"price-asc\">Price Low to High</option>\n                <option value=\"price-desc\">Price High to Low</option>\n              </select>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Product Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {products.data.map((product) => (\n          <ProductCard key={product.id} product={product} />\n        ))}\n      </div>\n\n      {/* Pagination */}\n      {products.pagination.totalPages > 1 && (\n        <div className=\"flex justify-center items-center space-x-2 pt-8\">\n          <button\n            onClick={() => handlePageChange(products.pagination.page - 1)}\n            disabled={!products.pagination.hasPrev}\n            className=\"px-3 py-2 border border-input rounded-md bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Previous\n          </button>\n          \n          <div className=\"flex space-x-1\">\n            {[...Array(Math.min(5, products.pagination.totalPages))].map((_, i) => {\n              const pageNum = Math.max(1, products.pagination.page - 2) + i\n              if (pageNum > products.pagination.totalPages) return null\n              \n              return (\n                <button\n                  key={pageNum}\n                  onClick={() => handlePageChange(pageNum)}\n                  className={cn(\n                    \"px-3 py-2 border rounded-md transition-colors\",\n                    pageNum === products.pagination.page\n                      ? \"bg-primary text-primary-foreground border-primary\"\n                      : \"border-input bg-background hover:bg-accent\"\n                  )}\n                >\n                  {pageNum}\n                </button>\n              )\n            })}\n          </div>\n\n          <button\n            onClick={() => handlePageChange(products.pagination.page + 1)}\n            disabled={!products.pagination.hasNext}\n            className=\"px-3 py-2 border border-input rounded-md bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            Next\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;;;AALA;;;;AAgBe,SAAS,YAAY,KAOjB;QAPiB,EAClC,eAAe,EACf,UAAU,CAAC,CAAC,EACZ,OAAO;QAAE,OAAO;QAAc,WAAW;IAAO,CAAC,EACjD,SAAS,EACT,cAAc,IAAI,EAClB,WAAW,IAAI,EACE,GAPiB;;IAQlC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAoC,mBAAmB;IAC/F,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC,CAAC;IACxC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAiB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAc;IAE5D,IAAA,0KAAS;iCAAC;YACR;QACF;gCAAG;QAAC;QAAgB;KAAY;IAEhC,MAAM,gBAAgB;YAAO,wEAAO;QAClC,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO;gBACP,WAAW,YAAY,KAAK;gBAC5B,eAAe,YAAY,SAAS;gBACpC,GAAG,OAAO,WAAW,CACnB,OAAO,OAAO,CAAC,gBAAgB,MAAM,CAAC;wBAAC,CAAC,GAAG,MAAM;2BAC/C,UAAU,aAAa,UAAU,QAAQ,UAAU;mBAEtD;YACH;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAuB,OAAP;YAC9C,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;YAAC,CAAC;IACvD;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;IAChB;IAEA,IAAI,WAAW,CAAC,UAAU;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;mBALT;;;;;;;;;;IAWlB;IAEA,IAAI,CAAC,YAAY,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;QAC3C,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAkC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACzF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,aAAa;;YAE7B,CAAC,eAAe,QAAQ,mBACvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BAAgC;4BACpC,SAAS,IAAI,CAAC,MAAM;4BAAC;4BAAK,SAAS,UAAU,CAAC,KAAK;4BAAC;;;;;;;oBAI9D,0BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAAsC;;;;;;0CAGtE,6LAAC;gCACC,IAAG;gCACH,OAAO,AAAC,GAAuB,OAArB,YAAY,KAAK,EAAC,KAAyB,OAAtB,YAAY,SAAS;gCACpD,UAAU,CAAC;oCACT,MAAM,CAAC,OAAO,UAAU,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;oCAChD,iBAAiB;wCAAE;wCAAO;oCAAU;gCACtC;gCACA,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,6LAAC;wCAAO,OAAM;kDAAiB;;;;;;kDAC/B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC,2JAAW;wBAAkB,SAAS;uBAArB,QAAQ,EAAE;;;;;;;;;;YAK/B,SAAS,UAAU,CAAC,UAAU,GAAG,mBAChC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,iBAAiB,SAAS,UAAU,CAAC,IAAI,GAAG;wBAC3D,UAAU,CAAC,SAAS,UAAU,CAAC,OAAO;wBACtC,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,UAAU,CAAC,UAAU;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG;4BAC/D,MAAM,UAAU,KAAK,GAAG,CAAC,GAAG,SAAS,UAAU,CAAC,IAAI,GAAG,KAAK;4BAC5D,IAAI,UAAU,SAAS,UAAU,CAAC,UAAU,EAAE,OAAO;4BAErD,qBACE,6LAAC;gCAEC,SAAS,IAAM,iBAAiB;gCAChC,WAAW,IAAA,4HAAE,EACX,iDACA,YAAY,SAAS,UAAU,CAAC,IAAI,GAChC,sDACA;0CAGL;+BATI;;;;;wBAYX;;;;;;kCAGF,6LAAC;wBACC,SAAS,IAAM,iBAAiB,SAAS,UAAU,CAAC,IAAI,GAAG;wBAC3D,UAAU,CAAC,SAAS,UAAU,CAAC,OAAO;wBACtC,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GAnLwB;KAAA", "debugId": null}}]}