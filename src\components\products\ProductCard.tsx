'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid'
import { Product } from '@/types/database'
import { formatCurrency, calculateDiscountPercentage, getImageUrl } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface ProductCardProps {
  product: Product
  className?: string
  showQuickAdd?: boolean
}

export default function ProductCard({ 
  product, 
  className,
  showQuickAdd = true 
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0]
  const hasDiscount = product.compare_price && product.compare_price > product.price
  const discountPercentage = hasDiscount 
    ? calculateDiscountPercentage(product.compare_price!, product.price)
    : 0

  const handleWishlistToggle = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsWishlisted(!isWishlisted)
    // TODO: Implement wishlist API call
  }

  const handleQuickAdd = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsLoading(true)
    try {
      // TODO: Implement add to cart API call
      console.log('Adding to cart:', product.id)
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn(
      "group relative bg-background border border-border rounded-xl overflow-hidden hover-lift hover-glow transition-all duration-500 animate-scale-in",
      className
    )}>
      <Link href={`/products/${product.slug}`}>
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-green-50 to-green-100">
          {primaryImage ? (
            <Image
              src={getImageUrl(primaryImage.url)}
              alt={primaryImage.alt_text || product.name}
              fill
              className="object-cover group-hover:scale-110 transition-transform duration-500 ease-out"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-50 to-green-100">
              <div className="text-center animate-float">
                <div className="text-4xl mb-2">🍎</div>
                <span className="text-muted-foreground text-sm">Fresh Product</span>
              </div>
            </div>
          )}

          {/* Discount Badge */}
          {hasDiscount && (
            <div className="absolute top-3 left-3 gradient-berry text-white text-xs font-bold px-3 py-1.5 rounded-full animate-bounce-gentle shadow-lg">
              <span className="flex items-center space-x-1">
                <span>🔥</span>
                <span>-{discountPercentage}%</span>
              </span>
            </div>
          )}

          {/* Featured Badge */}
          {product.is_featured && (
            <div className="absolute top-3 right-3 gradient-tropical text-white text-xs font-bold px-3 py-1.5 rounded-full animate-pulse-slow shadow-lg">
              <span className="flex items-center space-x-1">
                <span>⭐</span>
                <span>Featured</span>
              </span>
            </div>
          )}

          {/* Fresh Badge */}
          <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm text-green-700 text-xs font-medium px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <span className="flex items-center space-x-1">
              <span>🌱</span>
              <span>Fresh</span>
            </span>
          </div>

          {/* Wishlist Button */}
          <button
            onClick={handleWishlistToggle}
            className="absolute top-2 right-2 p-2 bg-background/80 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
            aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
          >
            {isWishlisted ? (
              <HeartIconSolid className="h-4 w-4 text-destructive" />
            ) : (
              <HeartIcon className="h-4 w-4 text-muted-foreground hover:text-destructive" />
            )}
          </button>

          {/* Quick Add Button */}
          {showQuickAdd && (
            <button
              onClick={handleQuickAdd}
              disabled={isLoading || product.quantity <= 0}
              className="absolute bottom-3 left-3 right-3 gradient-fresh text-white py-3 px-4 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover-scale disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-lg font-medium"
            >
              <ShoppingBagIcon className="h-4 w-4" />
              <span className="text-sm">
                {isLoading ? (
                  <span className="flex items-center space-x-1">
                    <span className="animate-spin">⏳</span>
                    <span>Adding...</span>
                  </span>
                ) : product.quantity <= 0 ? (
                  <span className="flex items-center space-x-1">
                    <span>❌</span>
                    <span>Out of Stock</span>
                  </span>
                ) : (
                  <span className="flex items-center space-x-1">
                    <span>🛒</span>
                    <span>Add to Cart</span>
                  </span>
                )}
              </span>
            </button>
          )}
        </div>

        {/* Product Info */}
        <div className="p-5 space-y-3">
          {/* Brand */}
          {product.brand && (
            <div className="flex items-center space-x-1">
              <span className="text-xs">🏷️</span>
              <p className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                {product.brand}
              </p>
            </div>
          )}

          {/* Product Name */}
          <h3 className="font-semibold text-foreground line-clamp-2 group-hover:text-primary transition-colors duration-300 text-lg">
            {product.name}
          </h3>

          {/* Short Description */}
          {product.short_description && (
            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
              {product.short_description}
            </p>
          )}

          {/* Price */}
          <div className="flex items-center space-x-3">
            <span className="text-xl font-bold text-primary">
              {formatCurrency(product.price)}
            </span>
            {hasDiscount && (
              <span className="text-sm text-muted-foreground line-through bg-red-50 px-2 py-1 rounded">
                {formatCurrency(product.compare_price!)}
              </span>
            )}
          </div>

          {/* Stock Status & Rating */}
          <div className="flex items-center justify-between pt-2 border-t border-border/50">
            <div className="flex items-center space-x-2">
              <div className={cn(
                "w-3 h-3 rounded-full animate-pulse-slow",
                product.quantity > 10 ? "bg-green-500" :
                product.quantity > 0 ? "bg-yellow-500" : "bg-red-500"
              )} />
              <span className="text-xs font-medium">
                {product.quantity > 10 ? (
                  <span className="text-green-600 flex items-center space-x-1">
                    <span>✅</span>
                    <span>Fresh & Available</span>
                  </span>
                ) : product.quantity > 0 ? (
                  <span className="text-yellow-600 flex items-center space-x-1">
                    <span>⚡</span>
                    <span>Only {product.quantity} left</span>
                  </span>
                ) : (
                  <span className="text-red-600 flex items-center space-x-1">
                    <span>❌</span>
                    <span>Out of Stock</span>
                  </span>
                )}
              </span>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-1">
              <div className="flex space-x-0.5">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className="w-3.5 h-3.5 text-yellow-400 fill-current hover-scale"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                  </svg>
                ))}
              </div>
              <span className="text-xs text-muted-foreground font-medium">(4.8)</span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  )
}
