'use client'

import { useState, useEffect } from 'react'
import { Product, PaginatedResponse, ProductFilters, ProductSort } from '@/types/database'
import ProductCard from './ProductCard'
import { cn } from '@/lib/utils'

interface ProductGridProps {
  initialProducts?: PaginatedResponse<Product>
  filters?: ProductFilters
  sort?: ProductSort
  className?: string
  showFilters?: boolean
  showSort?: boolean
}

export default function ProductGrid({
  initialProducts,
  filters = {},
  sort = { field: 'created_at', direction: 'desc' },
  className,
  showFilters = true,
  showSort = true
}: ProductGridProps) {
  const [products, setProducts] = useState<PaginatedResponse<Product> | null>(initialProducts || null)
  const [loading, setLoading] = useState(!initialProducts)
  const [currentFilters, setCurrentFilters] = useState<ProductFilters>(filters)
  const [currentSort, setCurrentSort] = useState<ProductSort>(sort)

  useEffect(() => {
    fetchProducts()
  }, [currentFilters, currentSort])

  const fetchProducts = async (page = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        sortField: currentSort.field,
        sortDirection: currentSort.direction,
        ...Object.fromEntries(
          Object.entries(currentFilters).filter(([_, value]) => 
            value !== undefined && value !== null && value !== ''
          )
        )
      })

      const response = await fetch(`/api/products?${params}`)
      if (!response.ok) throw new Error('Failed to fetch products')
      
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (newFilters: Partial<ProductFilters>) => {
    setCurrentFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleSortChange = (newSort: ProductSort) => {
    setCurrentSort(newSort)
  }

  const handlePageChange = (page: number) => {
    fetchProducts(page)
  }

  if (loading && !products) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(12)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="aspect-square bg-muted rounded-lg mb-4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
              <div className="h-6 bg-muted rounded w-1/3"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (!products || products.data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4a1 1 0 00-1-1H9a1 1 0 00-1 1v1" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-foreground mb-2">No products found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search criteria or browse our categories to find what you're looking for.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Filters and Sort */}
      {(showFilters || showSort) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 border-b pb-4">
          {/* Results count */}
          <div className="text-sm text-muted-foreground">
            Showing {products.data.length} of {products.pagination.total} products
          </div>

          {/* Sort dropdown */}
          {showSort && (
            <div className="flex items-center space-x-2">
              <label htmlFor="sort" className="text-sm font-medium text-foreground">
                Sort by:
              </label>
              <select
                id="sort"
                value={`${currentSort.field}-${currentSort.direction}`}
                onChange={(e) => {
                  const [field, direction] = e.target.value.split('-') as [ProductSort['field'], ProductSort['direction']]
                  handleSortChange({ field, direction })
                }}
                className="px-3 py-1 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="created_at-desc">Newest First</option>
                <option value="created_at-asc">Oldest First</option>
                <option value="name-asc">Name A-Z</option>
                <option value="name-desc">Name Z-A</option>
                <option value="price-asc">Price Low to High</option>
                <option value="price-desc">Price High to Low</option>
              </select>
            </div>
          )}
        </div>
      )}

      {/* Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {products.data.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {/* Pagination */}
      {products.pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2 pt-8">
          <button
            onClick={() => handlePageChange(products.pagination.page - 1)}
            disabled={!products.pagination.hasPrev}
            className="px-3 py-2 border border-input rounded-md bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Previous
          </button>
          
          <div className="flex space-x-1">
            {[...Array(Math.min(5, products.pagination.totalPages))].map((_, i) => {
              const pageNum = Math.max(1, products.pagination.page - 2) + i
              if (pageNum > products.pagination.totalPages) return null
              
              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={cn(
                    "px-3 py-2 border rounded-md transition-colors",
                    pageNum === products.pagination.page
                      ? "bg-primary text-primary-foreground border-primary"
                      : "border-input bg-background hover:bg-accent"
                  )}
                >
                  {pageNum}
                </button>
              )
            })}
          </div>

          <button
            onClick={() => handlePageChange(products.pagination.page + 1)}
            disabled={!products.pagination.hasNext}
            className="px-3 py-2 border border-input rounded-md bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}
