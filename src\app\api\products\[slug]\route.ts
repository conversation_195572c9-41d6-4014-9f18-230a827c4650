import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const { data: product, error } = await supabaseAdmin
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*),
        variants:product_variants(*),
        specifications:product_specifications(*)
      `)
      .eq('slug', slug)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('Error fetching product:', error)
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Sort images by sort_order
    if (product.images) {
      product.images.sort((a: any, b: any) => a.sort_order - b.sort_order)
    }

    // Sort variants by name
    if (product.variants) {
      product.variants.sort((a: any, b: any) => a.name.localeCompare(b.name))
    }

    // Sort specifications by sort_order
    if (product.specifications) {
      product.specifications.sort((a: any, b: any) => a.sort_order - b.sort_order)
    }

    return NextResponse.json({ data: product })
  } catch (error) {
    console.error('Error in product detail API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    const body = await request.json()

    const { data: product, error } = await supabaseAdmin
      .from('products')
      .update(body)
      .eq('slug', slug)
      .select()
      .single()

    if (error) {
      console.error('Error updating product:', error)
      return NextResponse.json(
        { error: 'Failed to update product' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data: product })
  } catch (error) {
    console.error('Error in product update API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params

    const { error } = await supabaseAdmin
      .from('products')
      .update({ is_active: false })
      .eq('slug', slug)

    if (error) {
      console.error('Error deleting product:', error)
      return NextResponse.json(
        { error: 'Failed to delete product' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Product deleted successfully' })
  } catch (error) {
    console.error('Error in product delete API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
