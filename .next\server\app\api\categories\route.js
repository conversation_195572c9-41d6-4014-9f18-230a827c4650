var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/categories/route.js")
R.c("server/chunks/node_modules_@supabase_node-fetch_lib_index_d6dc7176.js")
R.c("server/chunks/node_modules_next_bdeb0957._.js")
R.c("server/chunks/node_modules_tr46_3e4df63f._.js")
R.c("server/chunks/node_modules_@supabase_auth-js_dist_module_0e404d3a._.js")
R.c("server/chunks/node_modules_a45a867b._.js")
R.c("server/chunks/[root-of-the-server]__f4c73550._.js")
R.m("[project]/.next-internal/server/app/api/categories/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/categories/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/categories/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
