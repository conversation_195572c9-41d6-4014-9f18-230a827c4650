import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { Category } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeChildren = searchParams.get('includeChildren') === 'true'
    const parentId = searchParams.get('parentId')
    const active = searchParams.get('active') !== 'false' // Default to true

    let query = supabaseAdmin
      .from('categories')
      .select('*')
      .order('sort_order', { ascending: true })

    if (active) {
      query = query.eq('is_active', true)
    }

    if (parentId === 'null' || parentId === '') {
      query = query.is('parent_id', null)
    } else if (parentId) {
      query = query.eq('parent_id', parentId)
    }

    const { data: categories, error } = await query

    if (error) {
      console.error('Error fetching categories:', error)
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      )
    }

    // If includeChildren is true, fetch children for each category
    if (includeChildren && categories) {
      const categoriesWithChildren = await Promise.all(
        categories.map(async (category: Category) => {
          const { data: children } = await supabaseAdmin
            .from('categories')
            .select('*')
            .eq('parent_id', category.id)
            .eq('is_active', true)
            .order('sort_order', { ascending: true })

          return {
            ...category,
            children: children || []
          }
        })
      )

      return NextResponse.json({ data: categoriesWithChildren })
    }

    return NextResponse.json({ data: categories })
  } catch (error) {
    console.error('Error in categories API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, slug, description, image_url, parent_id, sort_order } = body

    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Name and slug are required' },
        { status: 400 }
      )
    }

    const { data: category, error } = await supabaseAdmin
      .from('categories')
      .insert({
        name,
        slug,
        description,
        image_url,
        parent_id,
        sort_order: sort_order || 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating category:', error)
      return NextResponse.json(
        { error: 'Failed to create category' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data: category }, { status: 201 })
  } catch (error) {
    console.error('Error in categories POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
