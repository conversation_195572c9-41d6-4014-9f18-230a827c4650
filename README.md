# SachMart - Modern E-commerce Website

A modern, responsive e-commerce website built with Next.js, TypeScript, Tailwind CSS, and Supabase.

## Features

### Core Functionality
- ✅ Product catalog with detailed product pages
- ✅ Category browsing with hierarchical navigation
- ✅ Product search and filtering capabilities
- ✅ Shopping cart functionality
- ✅ Product listing pages with pagination
- ✅ Responsive design with mobile-first approach
- ✅ SEO optimization with Next.js features

### Technical Features
- ✅ Next.js 15 with App Router
- ✅ TypeScript for type safety
- ✅ Tailwind CSS for styling
- ✅ Supabase for database and authentication
- ✅ Image optimization with Next.js Image component
- ✅ API routes for data fetching
- ✅ Row Level Security (RLS) for data protection

### UI/UX Features
- ✅ Clean, modern design
- ✅ Mobile-optimized interface
- ✅ Touch-friendly buttons and interactions
- ✅ Responsive navigation with hamburger menu
- ✅ Product image galleries
- ✅ Loading states and error handling

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sach-mart
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**

   a. Create a new project at [supabase.com](https://supabase.com)

   b. Go to Settings > API to get your project URL and anon key

   c. Copy `.env.local` and fill in your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Set up the database**

   a. Go to your Supabase project dashboard

   b. Navigate to the SQL Editor

   c. Copy and paste the contents of `supabase-schema.sql` and run it

   d. (Optional) Copy and paste the contents of `sample-data.sql` to add sample products

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   │   ├── categories/    # Category endpoints
│   │   ├── products/      # Product endpoints
│   │   └── cart/          # Cart endpoints
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components (Header, Footer)
│   └── products/         # Product-related components
├── lib/                  # Utility functions
│   ├── supabase.ts       # Supabase client configuration
│   └── utils.ts          # Helper functions
└── types/                # TypeScript type definitions
    └── database.ts       # Database schema types
```

## Database Schema

The application uses the following main tables:

- **categories** - Product categories with hierarchical support
- **products** - Main product information
- **product_images** - Product image gallery
- **product_variants** - Product variations (size, color, etc.)
- **product_specifications** - Product technical specifications
- **user_profiles** - Extended user information
- **cart_items** - Shopping cart items
- **orders** - Order information
- **order_items** - Individual order line items

## API Endpoints

### Categories
- `GET /api/categories` - List all categories
- `POST /api/categories` - Create a new category

### Products
- `GET /api/products` - List products with filtering and pagination
- `POST /api/products` - Create a new product
- `GET /api/products/[slug]` - Get product details
- `PUT /api/products/[slug]` - Update product
- `DELETE /api/products/[slug]` - Delete product

### Cart
- `GET /api/cart` - Get cart items
- `POST /api/cart` - Add item to cart
- `PUT /api/cart` - Update cart item quantity
- `DELETE /api/cart` - Remove item from cart

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

## License

This project is licensed under the MIT License.
