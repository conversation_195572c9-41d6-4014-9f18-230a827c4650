-- Insert sample categories
INSERT INTO categories (name, slug, description, image_url, sort_order) VALUES
('Electronics', 'electronics', 'Latest electronic devices and gadgets', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=500', 1),
('Clothing', 'clothing', 'Fashion and apparel for all occasions', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=500', 2),
('Home & Garden', 'home-garden', 'Everything for your home and garden', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500', 3),
('Sports & Outdoors', 'sports-outdoors', 'Sports equipment and outdoor gear', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500', 4),
('Books', 'books', 'Books for all ages and interests', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500', 5);

-- Insert subcategories
INSERT INTO categories (name, slug, description, parent_id, sort_order) VALUES
('Smartphones', 'smartphones', 'Latest smartphones and accessories', (SELECT id FROM categories WHERE slug = 'electronics'), 1),
('Laptops', 'laptops', 'Laptops and computers', (SELECT id FROM categories WHERE slug = 'electronics'), 2),
('Headphones', 'headphones', 'Audio equipment and headphones', (SELECT id FROM categories WHERE slug = 'electronics'), 3),
('Men''s Clothing', 'mens-clothing', 'Clothing for men', (SELECT id FROM categories WHERE slug = 'clothing'), 1),
('Women''s Clothing', 'womens-clothing', 'Clothing for women', (SELECT id FROM categories WHERE slug = 'clothing'), 2),
('Shoes', 'shoes', 'Footwear for all occasions', (SELECT id FROM categories WHERE slug = 'clothing'), 3);

-- Insert sample products
INSERT INTO products (name, slug, description, short_description, price, compare_price, sku, category_id, brand, tags, is_featured) VALUES
(
  'iPhone 15 Pro',
  'iphone-15-pro',
  'The iPhone 15 Pro features a titanium design, A17 Pro chip, and advanced camera system with 5x telephoto zoom.',
  'Latest iPhone with titanium design and A17 Pro chip',
  999.00,
  1099.00,
  'IPHONE15PRO',
  (SELECT id FROM categories WHERE slug = 'smartphones'),
  'Apple',
  ARRAY['smartphone', 'apple', 'ios', 'premium'],
  true
),
(
  'MacBook Air M3',
  'macbook-air-m3',
  'The new MacBook Air with M3 chip delivers exceptional performance and up to 18 hours of battery life.',
  'Powerful laptop with M3 chip and all-day battery',
  1199.00,
  1299.00,
  'MACBOOKAIRM3',
  (SELECT id FROM categories WHERE slug = 'laptops'),
  'Apple',
  ARRAY['laptop', 'apple', 'macbook', 'portable'],
  true
),
(
  'Sony WH-1000XM5',
  'sony-wh-1000xm5',
  'Industry-leading noise canceling headphones with exceptional sound quality and 30-hour battery life.',
  'Premium noise-canceling headphones',
  399.99,
  449.99,
  'SONYWH1000XM5',
  (SELECT id FROM categories WHERE slug = 'headphones'),
  'Sony',
  ARRAY['headphones', 'wireless', 'noise-canceling', 'premium'],
  true
),
(
  'Classic Cotton T-Shirt',
  'classic-cotton-tshirt',
  'Comfortable 100% cotton t-shirt perfect for everyday wear. Available in multiple colors and sizes.',
  'Comfortable everyday cotton t-shirt',
  24.99,
  29.99,
  'COTTONTEE001',
  (SELECT id FROM categories WHERE slug = 'mens-clothing'),
  'BasicWear',
  ARRAY['t-shirt', 'cotton', 'casual', 'basic'],
  false
),
(
  'Wireless Bluetooth Earbuds',
  'wireless-bluetooth-earbuds',
  'True wireless earbuds with active noise cancellation and 24-hour battery life with charging case.',
  'True wireless earbuds with ANC',
  149.99,
  199.99,
  'BTEARBUDS001',
  (SELECT id FROM categories WHERE slug = 'headphones'),
  'TechSound',
  ARRAY['earbuds', 'wireless', 'bluetooth', 'portable'],
  false
),
(
  'Running Shoes',
  'running-shoes',
  'Lightweight running shoes with advanced cushioning and breathable mesh upper for maximum comfort.',
  'Comfortable running shoes for athletes',
  89.99,
  119.99,
  'RUNSHOES001',
  (SELECT id FROM categories WHERE slug = 'shoes'),
  'SportMax',
  ARRAY['shoes', 'running', 'athletic', 'comfortable'],
  false
);

-- Insert product images
INSERT INTO product_images (product_id, url, alt_text, sort_order, is_primary) VALUES
-- iPhone 15 Pro images
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800', 'iPhone 15 Pro front view', 1, true),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=800', 'iPhone 15 Pro back view', 2, false),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'https://images.unsplash.com/photo-1580910051074-3eb694886505?w=800', 'iPhone 15 Pro side view', 3, false),

-- MacBook Air M3 images
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=800', 'MacBook Air M3 open view', 1, true),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=800', 'MacBook Air M3 closed view', 2, false),

-- Sony WH-1000XM5 images
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=800', 'Sony WH-1000XM5 headphones', 1, true),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=800', 'Sony headphones side view', 2, false),

-- T-shirt images
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=800', 'Classic cotton t-shirt', 1, true),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=800', 'T-shirt back view', 2, false),

-- Earbuds images
((SELECT id FROM products WHERE slug = 'wireless-bluetooth-earbuds'), 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=800', 'Wireless earbuds in case', 1, true),
((SELECT id FROM products WHERE slug = 'wireless-bluetooth-earbuds'), 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=800', 'Earbuds close-up', 2, false),

-- Running shoes images
((SELECT id FROM products WHERE slug = 'running-shoes'), 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=800', 'Running shoes pair', 1, true),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=800', 'Running shoes side view', 2, false);

-- Insert product specifications
INSERT INTO product_specifications (product_id, name, value, sort_order) VALUES
-- iPhone 15 Pro specs
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Display', '6.1-inch Super Retina XDR display', 1),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Chip', 'A17 Pro chip', 2),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Camera', 'Pro camera system with 48MP Main camera', 3),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Storage', '128GB, 256GB, 512GB, 1TB', 4),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Battery', 'Up to 23 hours video playback', 5),

-- MacBook Air M3 specs
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Processor', 'Apple M3 chip with 8-core CPU', 1),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Memory', '8GB or 16GB unified memory', 2),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Storage', '256GB, 512GB, 1TB, 2TB SSD', 3),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Display', '13.6-inch Liquid Retina display', 4),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Battery', 'Up to 18 hours battery life', 5),

-- Sony WH-1000XM5 specs
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Driver', '30mm driver unit', 1),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Battery Life', 'Up to 30 hours with ANC', 2),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Connectivity', 'Bluetooth 5.2, NFC', 3),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Weight', '250g', 4),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Noise Canceling', 'Industry-leading ANC', 5);

-- Insert product variants (for t-shirt sizes and colors)
INSERT INTO product_variants (product_id, name, price, sku, options, quantity) VALUES
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - Black', 24.99, 'COTTONTEE001-S-BLK', '{"size": "S", "color": "Black"}', 50),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - Black', 24.99, 'COTTONTEE001-M-BLK', '{"size": "M", "color": "Black"}', 75),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - Black', 24.99, 'COTTONTEE001-L-BLK', '{"size": "L", "color": "Black"}', 60),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - White', 24.99, 'COTTONTEE001-S-WHT', '{"size": "S", "color": "White"}', 45),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - White', 24.99, 'COTTONTEE001-M-WHT', '{"size": "M", "color": "White"}', 80),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - White', 24.99, 'COTTONTEE001-L-WHT', '{"size": "L", "color": "White"}', 55),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - Navy', 24.99, 'COTTONTEE001-S-NVY', '{"size": "S", "color": "Navy"}', 40),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - Navy', 24.99, 'COTTONTEE001-M-NVY', '{"size": "M", "color": "Navy"}', 65),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - Navy', 24.99, 'COTTONTEE001-L-NVY', '{"size": "L", "color": "Navy"}', 50);

-- Insert running shoes variants (sizes)
INSERT INTO product_variants (product_id, name, price, sku, options, quantity) VALUES
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 8', 89.99, 'RUNSHOES001-8', '{"size": "8"}', 25),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 9', 89.99, 'RUNSHOES001-9', '{"size": "9"}', 30),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 10', 89.99, 'RUNSHOES001-10', '{"size": "10"}', 35),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 11', 89.99, 'RUNSHOES001-11', '{"size": "11"}', 28),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 12', 89.99, 'RUNSHOES001-12', '{"size": "12"}', 20);
