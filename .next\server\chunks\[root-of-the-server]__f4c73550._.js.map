{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// For client-side operations\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations (API routes)\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n)\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,IAAA,mMAAmB,EAAC,aAAa;AAGlD,MAAM,gBAAgB,IAAA,yMAAY,EACvC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/app/api/categories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { supabaseAdmin } from '@/lib/supabase'\nimport { Category } from '@/types/database'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const includeChildren = searchParams.get('includeChildren') === 'true'\n    const parentId = searchParams.get('parentId')\n    const active = searchParams.get('active') !== 'false' // Default to true\n\n    let query = supabaseAdmin\n      .from('categories')\n      .select('*')\n      .order('sort_order', { ascending: true })\n\n    if (active) {\n      query = query.eq('is_active', true)\n    }\n\n    if (parentId === 'null' || parentId === '') {\n      query = query.is('parent_id', null)\n    } else if (parentId) {\n      query = query.eq('parent_id', parentId)\n    }\n\n    const { data: categories, error } = await query\n\n    if (error) {\n      console.error('Error fetching categories:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch categories' },\n        { status: 500 }\n      )\n    }\n\n    // If includeChildren is true, fetch children for each category\n    if (includeChildren && categories) {\n      const categoriesWithChildren = await Promise.all(\n        categories.map(async (category: Category) => {\n          const { data: children } = await supabaseAdmin\n            .from('categories')\n            .select('*')\n            .eq('parent_id', category.id)\n            .eq('is_active', true)\n            .order('sort_order', { ascending: true })\n\n          return {\n            ...category,\n            children: children || []\n          }\n        })\n      )\n\n      return NextResponse.json({ data: categoriesWithChildren })\n    }\n\n    return NextResponse.json({ data: categories })\n  } catch (error) {\n    console.error('Error in categories API:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { name, slug, description, image_url, parent_id, sort_order } = body\n\n    if (!name || !slug) {\n      return NextResponse.json(\n        { error: 'Name and slug are required' },\n        { status: 400 }\n      )\n    }\n\n    const { data: category, error } = await supabaseAdmin\n      .from('categories')\n      .insert({\n        name,\n        slug,\n        description,\n        image_url,\n        parent_id,\n        sort_order: sort_order || 0\n      })\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating category:', error)\n      return NextResponse.json(\n        { error: 'Failed to create category' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ data: category }, { status: 201 })\n  } catch (error) {\n    console.error('Error in categories POST:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,kBAAkB,aAAa,GAAG,CAAC,uBAAuB;QAChE,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC,cAAc,QAAQ,kBAAkB;;QAExE,IAAI,QAAQ,yIAAa,CACtB,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK;QAEzC,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,IAAI,aAAa,UAAU,aAAa,IAAI;YAC1C,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC,OAAO,IAAI,UAAU;YACnB,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,+DAA+D;QAC/D,IAAI,mBAAmB,YAAY;YACjC,MAAM,yBAAyB,MAAM,QAAQ,GAAG,CAC9C,WAAW,GAAG,CAAC,OAAO;gBACpB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,yIAAa,CAC3C,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,SAAS,EAAE,EAC3B,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBAEzC,OAAO;oBACL,GAAG,QAAQ;oBACX,UAAU,YAAY,EAAE;gBAC1B;YACF;YAGF,OAAO,gJAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAuB;QAC1D;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAW;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;QAEtE,IAAI,CAAC,QAAQ,CAAC,MAAM;YAClB,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6B,GACtC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,yIAAa,CAClD,IAAI,CAAC,cACL,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA,YAAY,cAAc;QAC5B,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAS,GAAG;YAAE,QAAQ;QAAI;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}