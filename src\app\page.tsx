import Link from 'next/link'
import Image from 'next/image'
import { ArrowRightIcon } from '@heroicons/react/24/outline'
import ProductGrid from '@/components/products/ProductGrid'
import { Category } from '@/types/database'

async function getFeaturedProducts() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/products?featured=true&limit=8`, {
      next: { revalidate: 300 } // Revalidate every 5 minutes
    })

    if (!response.ok) {
      throw new Error('Failed to fetch featured products')
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching featured products:', error)
    return { data: [], pagination: { page: 1, limit: 8, total: 0, totalPages: 0, hasNext: false, hasPrev: false } }
  }
}

async function getCategories() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/categories?includeChildren=false`, {
      next: { revalidate: 3600 } // Revalidate every hour
    })

    if (!response.ok) {
      throw new Error('Failed to fetch categories')
    }

    const result = await response.json()
    return result.data || []
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

export default async function Home() {
  const [featuredProducts, categories] = await Promise.all([
    getFeaturedProducts(),
    getCategories()
  ])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 gradient-fresh"></div>
<div
  className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2260%22 height=%2260%22 viewBox=%220 0 60 60%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.1%22%3E%3Ccircle cx=%2230%22 cy=%2230%22 r=%224%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"
/>

        <div className="relative container mx-auto px-4 py-20 lg:py-32">
          <div className="max-w-4xl">
            <div className="animate-slide-up">
              <div className="flex items-center space-x-3 mb-6">
                <span className="text-4xl animate-bounce-gentle">🍎</span>
                <span className="text-4xl animate-float">🥕</span>
                <span className="text-4xl animate-bounce-gentle" style={{animationDelay: '0.5s'}}>🍌</span>
                <span className="text-4xl animate-float" style={{animationDelay: '1s'}}>🍓</span>
              </div>

              <h1 className="text-5xl lg:text-7xl font-bold mb-6 text-white leading-tight">
                Fresh & Organic
                <span className="block text-yellow-300 animate-pulse-slow">Fruits & Vegetables</span>
              </h1>

              <p className="text-xl lg:text-2xl mb-8 text-white/95 leading-relaxed max-w-2xl">
                Farm-fresh produce delivered to your doorstep. 100% organic, locally sourced, and always fresh.
                <span className="inline-flex items-center space-x-1 ml-2">
                  <span>🌱</span>
                  <span className="font-semibold">Sustainably grown</span>
                </span>
              </p>

              <div className="flex flex-col sm:flex-row gap-4 animate-slide-in-left">
                <Link
                  href="/categories"
                  className="inline-flex items-center justify-center px-8 py-4 bg-white text-green-600 rounded-full font-bold text-lg hover-lift hover:shadow-xl transition-all duration-300 group"
                >
                  <span className="mr-2 group-hover:animate-bounce-gentle">🛒</span>
                  Shop Fresh Now
                  <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  href="/deals"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-white/30 text-white rounded-full font-bold text-lg hover:bg-white/10 transition-all duration-300 backdrop-blur-sm group"
                >
                  <span className="mr-2 group-hover:animate-bounce-gentle">🔥</span>
                  Today's Deals
                </Link>
              </div>

              <div className="mt-8 flex items-center space-x-6 text-white/90 animate-fade-in">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl animate-pulse-slow">🚚</span>
                  <span className="font-medium">Free Delivery</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-2xl animate-float">🌿</span>
                  <span className="font-medium">100% Organic</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-2xl animate-bounce-gentle">⚡</span>
                  <span className="font-medium">Same Day Fresh</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-10 text-6xl animate-float opacity-20">🍊</div>
        <div className="absolute bottom-20 left-10 text-5xl animate-bounce-gentle opacity-20">🥬</div>
        <div className="absolute top-1/2 right-20 text-4xl animate-pulse-slow opacity-20">🍇</div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-br from-green-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 animate-slide-up">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4 text-gray-800">Why Choose FreshMart?</h2>
            <p className="text-lg text-gray-600">Experience the freshest produce with unmatched quality and service</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center group animate-scale-in hover-lift">
              <div className="w-20 h-20 gradient-fresh rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-bounce-gentle shadow-lg">
                <span className="text-3xl">🚚</span>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Same-Day Fresh Delivery</h3>
              <p className="text-gray-600 leading-relaxed">Farm to your table in hours, not days. Free delivery on orders over $30 with our eco-friendly vehicles.</p>
              <div className="mt-4 inline-flex items-center text-green-600 font-medium">
                <span className="mr-1">🌱</span>
                <span>Carbon Neutral Delivery</span>
              </div>
            </div>

            <div className="text-center group animate-scale-in hover-lift" style={{animationDelay: '0.2s'}}>
              <div className="w-20 h-20 gradient-citrus rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-bounce-gentle shadow-lg">
                <span className="text-3xl">🌿</span>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">100% Organic Certified</h3>
              <p className="text-gray-600 leading-relaxed">Pesticide-free, non-GMO produce sourced directly from certified organic farms in your region.</p>
              <div className="mt-4 inline-flex items-center text-orange-600 font-medium">
                <span className="mr-1">🏆</span>
                <span>USDA Organic Certified</span>
              </div>
            </div>

            <div className="text-center group animate-scale-in hover-lift" style={{animationDelay: '0.4s'}}>
              <div className="w-20 h-20 gradient-berry rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-bounce-gentle shadow-lg">
                <span className="text-3xl">💚</span>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Freshness Guarantee</h3>
              <p className="text-gray-600 leading-relaxed">Not satisfied? Get a full refund or replacement. We guarantee the freshest produce or your money back.</p>
              <div className="mt-4 inline-flex items-center text-red-600 font-medium">
                <span className="mr-1">✅</span>
                <span>100% Satisfaction Promise</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-slide-up">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 text-gray-800">
              Fresh Categories
              <span className="block text-2xl lg:text-3xl text-green-600 font-normal mt-2">🌈 Taste the Rainbow of Nature</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">From crisp vegetables to sweet fruits, discover our carefully curated selection of farm-fresh produce</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            {categories.slice(0, 5).map((category: Category, index) => (
              <Link
                key={category.id}
                href={`/category/${category.slug}`}
                className="group text-center animate-scale-in hover-lift"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="relative aspect-square bg-gradient-to-br from-green-100 to-green-200 rounded-2xl mb-4 overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={category.name}
                      width={200}
                      height={200}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  ) : (
                    <div className="w-full h-full flex flex-col items-center justify-center">
                      <div className="text-5xl mb-2 animate-float">
                        {index === 0 ? '🥬' : index === 1 ? '🍎' : index === 2 ? '🥕' : index === 3 ? '🍓' : '🌽'}
                      </div>
                      <span className="text-sm text-gray-600 font-medium">Fresh & Organic</span>
                    </div>
                  )}

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Fresh Badge */}
                  <div className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    Fresh
                  </div>
                </div>

                <h3 className="font-bold text-lg group-hover:text-green-600 transition-colors duration-300 mb-1">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-500 group-hover:text-gray-700 transition-colors">
                  Farm Fresh Daily
                </p>
              </Link>
            ))}
          </div>

          <div className="text-center mt-12 animate-fade-in">
            <Link
              href="/categories"
              className="inline-flex items-center justify-center px-8 py-4 gradient-fresh text-white rounded-full font-bold text-lg hover-lift hover:shadow-xl transition-all duration-300 group"
            >
              <span className="mr-2 group-hover:animate-bounce-gentle">🛒</span>
              Explore All Categories
              <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-gradient-to-br from-orange-50 to-red-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 animate-slide-up">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <span className="text-4xl animate-bounce-gentle">⭐</span>
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-800">Today's Fresh Picks</h2>
              <span className="text-4xl animate-bounce-gentle">⭐</span>
            </div>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Handpicked by our experts, these premium organic products are at their peak freshness today.
              <span className="inline-flex items-center space-x-1 ml-2">
                <span>🌟</span>
                <span className="font-semibold text-orange-600">Limited time offers</span>
              </span>
            </p>
          </div>

          <div className="animate-fade-in">
            <ProductGrid
              initialProducts={featuredProducts}
              showFilters={false}
              showSort={false}
            />
          </div>

          <div className="text-center mt-12 animate-scale-in">
            <Link
              href="/products"
              className="inline-flex items-center justify-center px-8 py-4 gradient-citrus text-white rounded-full font-bold text-lg hover-lift hover:shadow-xl transition-all duration-300 group"
            >
              <span className="mr-2 group-hover:animate-bounce-gentle">🍊</span>
              Discover More Fresh Products
              <ArrowRightIcon className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-20 gradient-fresh text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>

        <div className="relative container mx-auto px-4 text-center">
          <div className="max-w-2xl mx-auto animate-slide-up">
            <div className="flex items-center justify-center space-x-2 mb-6">
              <span className="text-4xl animate-float">📧</span>
              <h2 className="text-3xl lg:text-4xl font-bold">Stay Fresh with Us!</h2>
              <span className="text-4xl animate-float" style={{animationDelay: '1s'}}>🌱</span>
            </div>

            <p className="text-xl mb-8 text-white/95">
              Get weekly fresh produce updates, seasonal recipes, and exclusive organic deals delivered to your inbox.
            </p>

            <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email for fresh updates"
                className="flex-1 px-6 py-3 rounded-full text-gray-800 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300"
              />
              <button
                type="submit"
                className="px-8 py-3 bg-white text-green-600 rounded-full font-bold hover-scale hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-2"
              >
                <span>🚀</span>
                <span>Subscribe</span>
              </button>
            </form>

            <p className="text-sm text-white/80 mt-4">
              Join 10,000+ happy customers who get the freshest deals first!
              <span className="inline-flex items-center space-x-1 ml-1">
                <span>🎉</span>
                <span>No spam, just fresh content</span>
              </span>
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
