{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// For client-side operations\nexport const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations (API routes)\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!\n)\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,IAAA,mMAAmB,EAAC,aAAa;AAGlD,MAAM,gBAAgB,IAAA,yMAAY,EACvC,aACA,QAAQ,GAAG,CAAC,yBAAyB", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { supabaseAdmin } from '@/lib/supabase'\nimport { ProductFilters, ProductSort } from '@/types/database'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    \n    // Pagination\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '12')\n    const offset = (page - 1) * limit\n\n    // Filters\n    const filters: ProductFilters = {\n      category: searchParams.get('category') || undefined,\n      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,\n      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,\n      brand: searchParams.get('brand') || undefined,\n      tags: searchParams.getAll('tags[]'),\n      inStock: searchParams.get('inStock') === 'true',\n      featured: searchParams.get('featured') === 'true',\n      search: searchParams.get('search') || undefined\n    }\n\n    // Sorting\n    const sortField = (searchParams.get('sortField') || 'created_at') as ProductSort['field']\n    const sortDirection = (searchParams.get('sortDirection') || 'desc') as ProductSort['direction']\n\n    // Build query\n    let query = supabaseAdmin\n      .from('products')\n      .select(`\n        *,\n        category:categories(*),\n        images:product_images(*),\n        variants:product_variants(*),\n        specifications:product_specifications(*)\n      `)\n      .eq('is_active', true)\n\n    // Apply filters\n    if (filters.category) {\n      query = query.eq('category_id', filters.category)\n    }\n\n    if (filters.minPrice !== undefined) {\n      query = query.gte('price', filters.minPrice)\n    }\n\n    if (filters.maxPrice !== undefined) {\n      query = query.lte('price', filters.maxPrice)\n    }\n\n    if (filters.brand) {\n      query = query.eq('brand', filters.brand)\n    }\n\n    if (filters.tags && filters.tags.length > 0) {\n      query = query.overlaps('tags', filters.tags)\n    }\n\n    if (filters.inStock) {\n      query = query.gt('quantity', 0)\n    }\n\n    if (filters.featured) {\n      query = query.eq('is_featured', true)\n    }\n\n    if (filters.search) {\n      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,brand.ilike.%${filters.search}%`)\n    }\n\n    // Apply sorting\n    query = query.order(sortField, { ascending: sortDirection === 'asc' })\n\n    // Get total count for pagination\n    const { count } = await supabaseAdmin\n      .from('products')\n      .select('*', { count: 'exact', head: true })\n      .eq('is_active', true)\n\n    // Apply pagination\n    query = query.range(offset, offset + limit - 1)\n\n    const { data: products, error } = await query\n\n    if (error) {\n      console.error('Error fetching products:', error)\n      return NextResponse.json(\n        { error: 'Failed to fetch products' },\n        { status: 500 }\n      )\n    }\n\n    // Calculate pagination info\n    const totalPages = Math.ceil((count || 0) / limit)\n    const hasNext = page < totalPages\n    const hasPrev = page > 1\n\n    return NextResponse.json({\n      data: products,\n      pagination: {\n        page,\n        limit,\n        total: count || 0,\n        totalPages,\n        hasNext,\n        hasPrev\n      }\n    })\n  } catch (error) {\n    console.error('Error in products API:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name,\n      slug,\n      description,\n      short_description,\n      price,\n      compare_price,\n      sku,\n      category_id,\n      brand,\n      tags,\n      is_featured,\n      quantity\n    } = body\n\n    if (!name || !slug || !price) {\n      return NextResponse.json(\n        { error: 'Name, slug, and price are required' },\n        { status: 400 }\n      )\n    }\n\n    const { data: product, error } = await supabaseAdmin\n      .from('products')\n      .insert({\n        name,\n        slug,\n        description,\n        short_description,\n        price,\n        compare_price,\n        sku,\n        category_id,\n        brand,\n        tags,\n        is_featured: is_featured || false,\n        quantity: quantity || 0\n      })\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error creating product:', error)\n      return NextResponse.json(\n        { error: 'Failed to create product' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ data: product }, { status: 201 })\n  } catch (error) {\n    console.error('Error in products POST:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,aAAa;QACb,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,UAAU;QACV,MAAM,UAA0B;YAC9B,UAAU,aAAa,GAAG,CAAC,eAAe;YAC1C,UAAU,aAAa,GAAG,CAAC,cAAc,WAAW,aAAa,GAAG,CAAC,eAAgB;YACrF,UAAU,aAAa,GAAG,CAAC,cAAc,WAAW,aAAa,GAAG,CAAC,eAAgB;YACrF,OAAO,aAAa,GAAG,CAAC,YAAY;YACpC,MAAM,aAAa,MAAM,CAAC;YAC1B,SAAS,aAAa,GAAG,CAAC,eAAe;YACzC,UAAU,aAAa,GAAG,CAAC,gBAAgB;YAC3C,QAAQ,aAAa,GAAG,CAAC,aAAa;QACxC;QAEA,UAAU;QACV,MAAM,YAAa,aAAa,GAAG,CAAC,gBAAgB;QACpD,MAAM,gBAAiB,aAAa,GAAG,CAAC,oBAAoB;QAE5D,cAAc;QACd,IAAI,QAAQ,yIAAa,CACtB,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,EAAE,CAAC,aAAa;QAEnB,gBAAgB;QAChB,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,QAAQ;QAClD;QAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,QAAQ,MAAM,GAAG,CAAC,SAAS,QAAQ,QAAQ;QAC7C;QAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;YAClC,QAAQ,MAAM,GAAG,CAAC,SAAS,QAAQ,QAAQ;QAC7C;QAEA,IAAI,QAAQ,KAAK,EAAE;YACjB,QAAQ,MAAM,EAAE,CAAC,SAAS,QAAQ,KAAK;QACzC;QAEA,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;YAC3C,QAAQ,MAAM,QAAQ,CAAC,QAAQ,QAAQ,IAAI;QAC7C;QAEA,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,MAAM,EAAE,CAAC,YAAY;QAC/B;QAEA,IAAI,QAAQ,QAAQ,EAAE;YACpB,QAAQ,MAAM,EAAE,CAAC,eAAe;QAClC;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,EAAE,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,qBAAqB,EAAE,QAAQ,MAAM,CAAC,eAAe,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;QACzH;QAEA,gBAAgB;QAChB,QAAQ,MAAM,KAAK,CAAC,WAAW;YAAE,WAAW,kBAAkB;QAAM;QAEpE,iCAAiC;QACjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yIAAa,CAClC,IAAI,CAAC,YACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,aAAa;QAEnB,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM;QAExC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI;QAC5C,MAAM,UAAU,OAAO;QACvB,MAAM,UAAU,OAAO;QAEvB,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,YAAY;gBACV;gBACA;gBACA,OAAO,SAAS;gBAChB;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,iBAAiB,EACjB,KAAK,EACL,aAAa,EACb,GAAG,EACH,WAAW,EACX,KAAK,EACL,IAAI,EACJ,WAAW,EACX,QAAQ,EACT,GAAG;QAEJ,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,yIAAa,CACjD,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aAAa,eAAe;YAC5B,UAAU,YAAY;QACxB,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gJAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAQ,GAAG;YAAE,QAAQ;QAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}