-- Insert sample categories
INSERT INTO categories (name, slug, description, image_url, sort_order) VALUES
('Fresh Fruits', 'fresh-fruits', 'Premium organic fruits picked fresh daily', 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=500', 1),
('Vegetables', 'vegetables', 'Farm-fresh organic vegetables and greens', 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=500', 2),
('Herbs & Spices', 'herbs-spices', 'Fresh herbs and organic spices', 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500', 3),
('Organic Dairy', 'organic-dairy', 'Fresh organic dairy products', 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=500', 4),
('Pantry Essentials', 'pantry-essentials', 'Organic pantry staples and grains', 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=500', 5);

-- Insert subcategories
INSERT INTO categories (name, slug, description, parent_id, sort_order) VALUES
('Citrus Fruits', 'citrus-fruits', 'Fresh oranges, lemons, limes and more', (SELECT id FROM categories WHERE slug = 'fresh-fruits'), 1),
('Berries', 'berries', 'Fresh strawberries, blueberries, raspberries', (SELECT id FROM categories WHERE slug = 'fresh-fruits'), 2),
('Tropical Fruits', 'tropical-fruits', 'Exotic tropical fruits and seasonal varieties', (SELECT id FROM categories WHERE slug = 'fresh-fruits'), 3),
('Leafy Greens', 'leafy-greens', 'Fresh spinach, kale, lettuce and salad greens', (SELECT id FROM categories WHERE slug = 'vegetables'), 1),
('Root Vegetables', 'root-vegetables', 'Carrots, potatoes, beets and root veggies', (SELECT id FROM categories WHERE slug = 'vegetables'), 2),
('Fresh Herbs', 'fresh-herbs', 'Basil, cilantro, parsley and cooking herbs', (SELECT id FROM categories WHERE slug = 'herbs-spices'), 1);

-- Insert sample products
INSERT INTO products (name, slug, description, short_description, price, compare_price, sku, category_id, brand, tags, is_featured) VALUES
(
  'Organic Honeycrisp Apples',
  'organic-honeycrisp-apples',
  'Premium organic Honeycrisp apples, crisp and sweet with the perfect balance of flavor. Grown without pesticides on certified organic farms.',
  'Sweet, crisp organic apples perfect for snacking',
  6.99,
  8.99,
  'APPLE001',
  (SELECT id FROM categories WHERE slug = 'fresh-fruits'),
  'Green Valley Farms',
  ARRAY['organic', 'apples', 'fresh', 'sweet', 'crisp'],
  true
),
(
  'Fresh Organic Spinach',
  'fresh-organic-spinach',
  'Baby spinach leaves, tender and nutrient-rich. Perfect for salads, smoothies, or cooking. Harvested fresh daily from our organic farms.',
  'Tender baby spinach leaves, nutrient-packed',
  4.49,
  5.99,
  'SPINACH001',
  (SELECT id FROM categories WHERE slug = 'leafy-greens'),
  'Sunshine Organics',
  ARRAY['organic', 'spinach', 'leafy-greens', 'healthy', 'fresh'],
  true
),
(
  'Organic Strawberries',
  'organic-strawberries',
  'Sweet, juicy organic strawberries at peak ripeness. Hand-picked and carefully selected for the best flavor and quality.',
  'Sweet, juicy organic strawberries',
  8.99,
  10.99,
  'STRAWBERRY001',
  (SELECT id FROM categories WHERE slug = 'berries'),
  'Berry Fresh Farms',
  ARRAY['organic', 'strawberries', 'berries', 'sweet', 'fresh'],
  true
),
(
  'Rainbow Carrots Bundle',
  'rainbow-carrots-bundle',
  'Colorful mix of orange, purple, and yellow organic carrots. Sweet, crunchy, and packed with vitamins and antioxidants.',
  'Colorful organic carrots, sweet and crunchy',
  5.49,
  6.99,
  'CARROT001',
  (SELECT id FROM categories WHERE slug = 'root-vegetables'),
  'Rainbow Harvest',
  ARRAY['organic', 'carrots', 'colorful', 'healthy', 'root-vegetables'],
  false
),
(
  'Fresh Basil Bunch',
  'fresh-basil-bunch',
  'Aromatic fresh basil leaves, perfect for cooking, garnishing, or making pesto. Grown in our greenhouse for year-round freshness.',
  'Aromatic fresh basil for cooking',
  3.99,
  4.99,
  'BASIL001',
  (SELECT id FROM categories WHERE slug = 'fresh-herbs'),
  'Herb Garden Co',
  ARRAY['organic', 'basil', 'herbs', 'aromatic', 'cooking'],
  false
),
(
  'Organic Avocados',
  'organic-avocados',
  'Creamy, perfectly ripe organic avocados. Rich in healthy fats and nutrients. Perfect for toast, salads, or guacamole.',
  'Creamy organic avocados, perfectly ripe',
  7.99,
  9.99,
  'AVOCADO001',
  (SELECT id FROM categories WHERE slug = 'fresh-fruits'),
  'Tropical Harvest',
  ARRAY['organic', 'avocados', 'healthy-fats', 'creamy', 'fresh'],
  true
);

-- Insert product images
INSERT INTO product_images (product_id, url, alt_text, sort_order, is_primary) VALUES
-- Organic Honeycrisp Apples images
((SELECT id FROM products WHERE slug = 'organic-honeycrisp-apples'), 'https://images.unsplash.com/photo-**********-1e4cd0b6cbd6?w=800', 'Fresh organic Honeycrisp apples', 1, true),
((SELECT id FROM products WHERE slug = 'organic-honeycrisp-apples'), 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=800', 'Honeycrisp apples close-up', 2, false),
((SELECT id FROM products WHERE slug = 'organic-honeycrisp-apples'), 'https://images.unsplash.com/photo-1619546813926-a78fa6372cd2?w=800', 'Apple orchard view', 3, false),

-- Fresh Organic Spinach images
((SELECT id FROM products WHERE slug = 'fresh-organic-spinach'), 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=800', 'Fresh organic spinach leaves', 1, true),
((SELECT id FROM products WHERE slug = 'fresh-organic-spinach'), 'https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=800', 'Spinach in bowl', 2, false),

-- Organic Strawberries images
((SELECT id FROM products WHERE slug = 'organic-strawberries'), 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=800', 'Fresh organic strawberries', 1, true),
((SELECT id FROM products WHERE slug = 'organic-strawberries'), 'https://images.unsplash.com/photo-1518635017498-87f514b751ba?w=800', 'Strawberries in basket', 2, false),

-- Rainbow Carrots images
((SELECT id FROM products WHERE slug = 'rainbow-carrots-bundle'), 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=800', 'Colorful rainbow carrots', 1, true),
((SELECT id FROM products WHERE slug = 'rainbow-carrots-bundle'), 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?w=800', 'Rainbow carrots bunch', 2, false),

-- Fresh Basil images
((SELECT id FROM products WHERE slug = 'fresh-basil-bunch'), 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?w=800', 'Fresh basil bunch', 1, true),
((SELECT id FROM products WHERE slug = 'fresh-basil-bunch'), 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800', 'Basil leaves close-up', 2, false),

-- Organic Avocados images
((SELECT id FROM products WHERE slug = 'organic-avocados'), 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=800', 'Fresh organic avocados', 1, true),
((SELECT id FROM products WHERE slug = 'organic-avocados'), 'https://images.unsplash.com/photo-1590301157890-4810ed352733?w=800', 'Avocados cut in half', 2, false);

-- Insert product specifications
INSERT INTO product_specifications (product_id, name, value, sort_order) VALUES
-- iPhone 15 Pro specs
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Display', '6.1-inch Super Retina XDR display', 1),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Chip', 'A17 Pro chip', 2),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Camera', 'Pro camera system with 48MP Main camera', 3),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Storage', '128GB, 256GB, 512GB, 1TB', 4),
((SELECT id FROM products WHERE slug = 'iphone-15-pro'), 'Battery', 'Up to 23 hours video playback', 5),

-- MacBook Air M3 specs
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Processor', 'Apple M3 chip with 8-core CPU', 1),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Memory', '8GB or 16GB unified memory', 2),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Storage', '256GB, 512GB, 1TB, 2TB SSD', 3),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Display', '13.6-inch Liquid Retina display', 4),
((SELECT id FROM products WHERE slug = 'macbook-air-m3'), 'Battery', 'Up to 18 hours battery life', 5),

-- Sony WH-1000XM5 specs
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Driver', '30mm driver unit', 1),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Battery Life', 'Up to 30 hours with ANC', 2),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Connectivity', 'Bluetooth 5.2, NFC', 3),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Weight', '250g', 4),
((SELECT id FROM products WHERE slug = 'sony-wh-1000xm5'), 'Noise Canceling', 'Industry-leading ANC', 5);

-- Insert product variants (for t-shirt sizes and colors)
INSERT INTO product_variants (product_id, name, price, sku, options, quantity) VALUES
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - Black', 24.99, 'COTTONTEE001-S-BLK', '{"size": "S", "color": "Black"}', 50),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - Black', 24.99, 'COTTONTEE001-M-BLK', '{"size": "M", "color": "Black"}', 75),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - Black', 24.99, 'COTTONTEE001-L-BLK', '{"size": "L", "color": "Black"}', 60),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - White', 24.99, 'COTTONTEE001-S-WHT', '{"size": "S", "color": "White"}', 45),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - White', 24.99, 'COTTONTEE001-M-WHT', '{"size": "M", "color": "White"}', 80),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - White', 24.99, 'COTTONTEE001-L-WHT', '{"size": "L", "color": "White"}', 55),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Small - Navy', 24.99, 'COTTONTEE001-S-NVY', '{"size": "S", "color": "Navy"}', 40),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Medium - Navy', 24.99, 'COTTONTEE001-M-NVY', '{"size": "M", "color": "Navy"}', 65),
((SELECT id FROM products WHERE slug = 'classic-cotton-tshirt'), 'Large - Navy', 24.99, 'COTTONTEE001-L-NVY', '{"size": "L", "color": "Navy"}', 50);

-- Insert running shoes variants (sizes)
INSERT INTO product_variants (product_id, name, price, sku, options, quantity) VALUES
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 8', 89.99, 'RUNSHOES001-8', '{"size": "8"}', 25),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 9', 89.99, 'RUNSHOES001-9', '{"size": "9"}', 30),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 10', 89.99, 'RUNSHOES001-10', '{"size": "10"}', 35),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 11', 89.99, 'RUNSHOES001-11', '{"size": "11"}', 28),
((SELECT id FROM products WHERE slug = 'running-shoes'), 'Size 12', 89.99, 'RUNSHOES001-12', '{"size": "12"}', 20);
