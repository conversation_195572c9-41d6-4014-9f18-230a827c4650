import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

async function getSupabaseUser() {
  const cookieStore = cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  const { data: { user } } = await supabase.auth.getUser()
  return user
}

export async function GET(request: NextRequest) {
  try {
    const user = await getSupabaseUser()
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!user && !sessionId) {
      return NextResponse.json(
        { error: 'User authentication or session ID required' },
        { status: 401 }
      )
    }

    let query = supabaseAdmin
      .from('cart_items')
      .select(`
        *,
        product:products(*),
        variant:product_variants(*)
      `)

    if (user) {
      query = query.eq('user_id', user.id)
    } else {
      query = query.eq('session_id', sessionId)
    }

    const { data: cartItems, error } = await query

    if (error) {
      console.error('Error fetching cart items:', error)
      return NextResponse.json(
        { error: 'Failed to fetch cart items' },
        { status: 500 }
      )
    }

    // Calculate cart summary
    const itemCount = cartItems?.reduce((sum, item) => sum + item.quantity, 0) || 0
    const subtotal = cartItems?.reduce((sum, item) => {
      const price = item.variant?.price || item.product?.price || 0
      return sum + (price * item.quantity)
    }, 0) || 0

    return NextResponse.json({
      data: {
        items: cartItems || [],
        itemCount,
        subtotal,
        total: subtotal // Add tax/shipping calculation here if needed
      }
    })
  } catch (error) {
    console.error('Error in cart GET API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getSupabaseUser()
    const body = await request.json()
    const { productId, variantId, quantity, sessionId } = body

    if (!productId || !quantity) {
      return NextResponse.json(
        { error: 'Product ID and quantity are required' },
        { status: 400 }
      )
    }

    if (!user && !sessionId) {
      return NextResponse.json(
        { error: 'User authentication or session ID required' },
        { status: 401 }
      )
    }

    // Check if item already exists in cart
    let existingQuery = supabaseAdmin
      .from('cart_items')
      .select('*')
      .eq('product_id', productId)

    if (variantId) {
      existingQuery = existingQuery.eq('variant_id', variantId)
    } else {
      existingQuery = existingQuery.is('variant_id', null)
    }

    if (user) {
      existingQuery = existingQuery.eq('user_id', user.id)
    } else {
      existingQuery = existingQuery.eq('session_id', sessionId)
    }

    const { data: existingItem } = await existingQuery.single()

    if (existingItem) {
      // Update existing item
      const { data: updatedItem, error } = await supabaseAdmin
        .from('cart_items')
        .update({ quantity: existingItem.quantity + quantity })
        .eq('id', existingItem.id)
        .select()
        .single()

      if (error) {
        console.error('Error updating cart item:', error)
        return NextResponse.json(
          { error: 'Failed to update cart item' },
          { status: 500 }
        )
      }

      return NextResponse.json({ data: updatedItem })
    } else {
      // Create new item
      const { data: newItem, error } = await supabaseAdmin
        .from('cart_items')
        .insert({
          user_id: user?.id,
          session_id: user ? null : sessionId,
          product_id: productId,
          variant_id: variantId,
          quantity
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating cart item:', error)
        return NextResponse.json(
          { error: 'Failed to add item to cart' },
          { status: 500 }
        )
      }

      return NextResponse.json({ data: newItem }, { status: 201 })
    }
  } catch (error) {
    console.error('Error in cart POST API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const user = await getSupabaseUser()
    const body = await request.json()
    const { id, quantity } = body

    if (!id || quantity === undefined) {
      return NextResponse.json(
        { error: 'Item ID and quantity are required' },
        { status: 400 }
      )
    }

    const { data: updatedItem, error } = await supabaseAdmin
      .from('cart_items')
      .update({ quantity })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating cart item:', error)
      return NextResponse.json(
        { error: 'Failed to update cart item' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data: updatedItem })
  } catch (error) {
    console.error('Error in cart PUT API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Item ID is required' },
        { status: 400 }
      )
    }

    const { error } = await supabaseAdmin
      .from('cart_items')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting cart item:', error)
      return NextResponse.json(
        { error: 'Failed to remove item from cart' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Item removed from cart' })
  } catch (error) {
    console.error('Error in cart DELETE API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
