{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-type": "application/json", "date": "Wed, 24 Sep 2025 02:36:57 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "rsc, next-router-state-tree, next-router-prefetch, next-router-segment-prefetch"}, "body": "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", "status": 200, "url": "http://localhost:3000/api/categories?includeChildren=true"}, "revalidate": 3600, "tags": []}