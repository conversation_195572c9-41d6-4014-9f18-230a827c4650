{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-type": "application/json", "date": "Wed, 24 Sep 2025 03:44:48 GMT", "keep-alive": "timeout=5", "transfer-encoding": "chunked", "vary": "rsc, next-router-state-tree, next-router-prefetch, next-router-segment-prefetch"}, "body": "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", "status": 200, "url": "http://localhost:3000/api/categories?includeChildren=false"}, "revalidate": 3600, "tags": []}