{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/products/ProductGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/products/ProductGrid.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductGrid.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/products/ProductGrid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/products/ProductGrid.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/products/ProductGrid.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport Image from 'next/image'\nimport { ArrowRightIcon, ShoppingBagIcon, TruckIcon, ShieldCheckIcon, CreditCardIcon } from '@heroicons/react/24/outline'\nimport ProductGrid from '@/components/products/ProductGrid'\nimport { Product, Category } from '@/types/database'\n\nasync function getFeaturedProducts() {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/products?featured=true&limit=8`, {\n      next: { revalidate: 300 } // Revalidate every 5 minutes\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch featured products')\n    }\n\n    return await response.json()\n  } catch (error) {\n    console.error('Error fetching featured products:', error)\n    return { data: [], pagination: { page: 1, limit: 8, total: 0, totalPages: 0, hasNext: false, hasPrev: false } }\n  }\n}\n\nasync function getCategories() {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/categories?includeChildren=false`, {\n      next: { revalidate: 3600 } // Revalidate every hour\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch categories')\n    }\n\n    const result = await response.json()\n    return result.data || []\n  } catch (error) {\n    console.error('Error fetching categories:', error)\n    return []\n  }\n}\n\nexport default async function Home() {\n  const [featuredProducts, categories] = await Promise.all([\n    getFeaturedProducts(),\n    getCategories()\n  ])\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-r from-primary to-primary/80 text-primary-foreground\">\n        <div className=\"container mx-auto px-4 py-20 lg:py-32\">\n          <div className=\"max-w-3xl\">\n            <h1 className=\"text-4xl lg:text-6xl font-bold mb-6\">\n              Discover Amazing Products at Great Prices\n            </h1>\n            <p className=\"text-xl lg:text-2xl mb-8 text-primary-foreground/90\">\n              Shop the latest electronics, fashion, home goods, and more with fast shipping and excellent customer service.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Link\n                href=\"/categories\"\n                className=\"inline-flex items-center justify-center px-8 py-3 bg-background text-foreground rounded-lg font-medium hover:bg-background/90 transition-colors\"\n              >\n                Shop Now\n                <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n              </Link>\n              <Link\n                href=\"/deals\"\n                className=\"inline-flex items-center justify-center px-8 py-3 border border-primary-foreground/20 text-primary-foreground rounded-lg font-medium hover:bg-primary-foreground/10 transition-colors\"\n              >\n                View Deals\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <TruckIcon className=\"h-8 w-8 text-primary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold mb-2\">Free Shipping</h3>\n              <p className=\"text-muted-foreground\">Free shipping on orders over $50. Fast and reliable delivery.</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <ShieldCheckIcon className=\"h-8 w-8 text-primary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold mb-2\">Secure Shopping</h3>\n              <p className=\"text-muted-foreground\">Your data is protected with industry-standard security measures.</p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <CreditCardIcon className=\"h-8 w-8 text-primary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold mb-2\">Easy Returns</h3>\n              <p className=\"text-muted-foreground\">30-day return policy. No questions asked.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"py-16\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold mb-4\">Shop by Category</h2>\n            <p className=\"text-muted-foreground text-lg\">Explore our wide range of product categories</p>\n          </div>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6\">\n            {categories.slice(0, 5).map((category: Category) => (\n              <Link\n                key={category.id}\n                href={`/category/${category.slug}`}\n                className=\"group text-center\"\n              >\n                <div className=\"aspect-square bg-muted rounded-lg mb-4 overflow-hidden\">\n                  {category.image_url ? (\n                    <Image\n                      src={category.image_url}\n                      alt={category.name}\n                      width={200}\n                      height={200}\n                      className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <div className=\"w-full h-full flex items-center justify-center bg-muted\">\n                      <ShoppingBagIcon className=\"h-12 w-12 text-muted-foreground\" />\n                    </div>\n                  )}\n                </div>\n                <h3 className=\"font-medium group-hover:text-primary transition-colors\">\n                  {category.name}\n                </h3>\n              </Link>\n            ))}\n          </div>\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/categories\"\n              className=\"inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors\"\n            >\n              View All Categories\n              <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products Section */}\n      <section className=\"py-16 bg-muted/50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold mb-4\">Featured Products</h2>\n            <p className=\"text-muted-foreground text-lg\">Discover our handpicked selection of amazing products</p>\n          </div>\n          <ProductGrid\n            initialProducts={featuredProducts}\n            showFilters={false}\n            showSort={false}\n          />\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/products\"\n              className=\"inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors\"\n            >\n              View All Products\n              <ArrowRightIcon className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAGA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,mCAAmC,CAAC,EAAE;YAC/H,MAAM;gBAAE,YAAY;YAAI,EAAE,6BAA6B;QACzD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,MAAM,EAAE;YAAE,YAAY;gBAAE,MAAM;gBAAG,OAAO;gBAAG,OAAO;gBAAG,YAAY;gBAAG,SAAS;gBAAO,SAAS;YAAM;QAAE;IAChH;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,qCAAqC,CAAC,EAAE;YACjI,MAAM;gBAAE,YAAY;YAAK,EAAE,wBAAwB;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAEe,eAAe;IAC5B,MAAM,CAAC,kBAAkB,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;QACvD;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC,6OAAc;gDAAC,WAAU;;;;;;;;;;;;kDAE5B,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8NAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gPAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6OAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAE/C,8OAAC;4BAAI,WAAU;sCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC3B,8OAAC,uKAAI;oCAEH,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;oCAClC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACZ,SAAS,SAAS,iBACjB,8OAAC,wIAAK;gDACJ,KAAK,SAAS,SAAS;gDACvB,KAAK,SAAS,IAAI;gDAClB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;qEAGZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gPAAe;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAG,WAAU;sDACX,SAAS,IAAI;;;;;;;mCApBX,SAAS,EAAE;;;;;;;;;;sCAyBtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,6OAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAE/C,8OAAC,wJAAW;4BACV,iBAAiB;4BACjB,aAAa;4BACb,UAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC,6OAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}]}