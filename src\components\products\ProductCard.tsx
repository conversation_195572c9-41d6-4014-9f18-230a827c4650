'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { HeartIcon, ShoppingBagIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid'
import { Product } from '@/types/database'
import { formatCurrency, calculateDiscountPercentage, getImageUrl } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface ProductCardProps {
  product: Product
  className?: string
  showQuickAdd?: boolean
}

export default function ProductCard({ 
  product, 
  className,
  showQuickAdd = true 
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const primaryImage = product.images?.find(img => img.is_primary) || product.images?.[0]
  const hasDiscount = product.compare_price && product.compare_price > product.price
  const discountPercentage = hasDiscount 
    ? calculateDiscountPercentage(product.compare_price!, product.price)
    : 0

  const handleWishlistToggle = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsWishlisted(!isWishlisted)
    // TODO: Implement wishlist API call
  }

  const handleQuickAdd = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    setIsLoading(true)
    try {
      // TODO: Implement add to cart API call
      console.log('Adding to cart:', product.id)
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn(
      "group relative bg-background border border-border rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300",
      className
    )}>
      <Link href={`/products/${product.slug}`}>
        {/* Product Image */}
        <div className="relative aspect-square overflow-hidden bg-muted">
          {primaryImage ? (
            <Image
              src={getImageUrl(primaryImage.url)}
              alt={primaryImage.alt_text || product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-muted">
              <span className="text-muted-foreground text-sm">No Image</span>
            </div>
          )}
          
          {/* Discount Badge */}
          {hasDiscount && (
            <div className="absolute top-2 left-2 bg-destructive text-destructive-foreground text-xs font-medium px-2 py-1 rounded">
              -{discountPercentage}%
            </div>
          )}

          {/* Featured Badge */}
          {product.is_featured && (
            <div className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded">
              Featured
            </div>
          )}

          {/* Wishlist Button */}
          <button
            onClick={handleWishlistToggle}
            className="absolute top-2 right-2 p-2 bg-background/80 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-background"
            aria-label={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}
          >
            {isWishlisted ? (
              <HeartIconSolid className="h-4 w-4 text-destructive" />
            ) : (
              <HeartIcon className="h-4 w-4 text-muted-foreground hover:text-destructive" />
            )}
          </button>

          {/* Quick Add Button */}
          {showQuickAdd && (
            <button
              onClick={handleQuickAdd}
              disabled={isLoading || product.quantity <= 0}
              className="absolute bottom-2 left-2 right-2 bg-primary text-primary-foreground py-2 px-4 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <ShoppingBagIcon className="h-4 w-4" />
              <span className="text-sm font-medium">
                {isLoading ? 'Adding...' : product.quantity <= 0 ? 'Out of Stock' : 'Quick Add'}
              </span>
            </button>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4 space-y-2">
          {/* Brand */}
          {product.brand && (
            <p className="text-xs text-muted-foreground uppercase tracking-wide">
              {product.brand}
            </p>
          )}

          {/* Product Name */}
          <h3 className="font-medium text-foreground line-clamp-2 group-hover:text-primary transition-colors">
            {product.name}
          </h3>

          {/* Short Description */}
          {product.short_description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {product.short_description}
            </p>
          )}

          {/* Price */}
          <div className="flex items-center space-x-2">
            <span className="text-lg font-semibold text-foreground">
              {formatCurrency(product.price)}
            </span>
            {hasDiscount && (
              <span className="text-sm text-muted-foreground line-through">
                {formatCurrency(product.compare_price!)}
              </span>
            )}
          </div>

          {/* Stock Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <div className={cn(
                "w-2 h-2 rounded-full",
                product.quantity > 10 ? "bg-green-500" : 
                product.quantity > 0 ? "bg-yellow-500" : "bg-red-500"
              )} />
              <span className="text-xs text-muted-foreground">
                {product.quantity > 10 ? 'In Stock' : 
                 product.quantity > 0 ? `${product.quantity} left` : 'Out of Stock'}
              </span>
            </div>

            {/* Rating placeholder */}
            <div className="flex items-center space-x-1">
              <div className="flex space-x-1">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className="w-3 h-3 text-yellow-400 fill-current"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                  </svg>
                ))}
              </div>
              <span className="text-xs text-muted-foreground">(4.5)</span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  )
}
