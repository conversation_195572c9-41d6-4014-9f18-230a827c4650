import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { ProductFilters, ProductSort } from '@/types/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '12')
    const offset = (page - 1) * limit

    // Filters
    const filters: ProductFilters = {
      category: searchParams.get('category') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      brand: searchParams.get('brand') || undefined,
      tags: searchParams.getAll('tags[]'),
      inStock: searchParams.get('inStock') === 'true',
      featured: searchParams.get('featured') === 'true',
      search: searchParams.get('search') || undefined
    }

    // Sorting
    const sortField = (searchParams.get('sortField') || 'created_at') as ProductSort['field']
    const sortDirection = (searchParams.get('sortDirection') || 'desc') as ProductSort['direction']

    // Build query
    let query = supabaseAdmin
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*),
        variants:product_variants(*),
        specifications:product_specifications(*)
      `)
      .eq('is_active', true)

    // Apply filters
    if (filters.category) {
      query = query.eq('category_id', filters.category)
    }

    if (filters.minPrice !== undefined) {
      query = query.gte('price', filters.minPrice)
    }

    if (filters.maxPrice !== undefined) {
      query = query.lte('price', filters.maxPrice)
    }

    if (filters.brand) {
      query = query.eq('brand', filters.brand)
    }

    if (filters.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags)
    }

    if (filters.inStock) {
      query = query.gt('quantity', 0)
    }

    if (filters.featured) {
      query = query.eq('is_featured', true)
    }

    if (filters.search) {
      query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%,brand.ilike.%${filters.search}%`)
    }

    // Apply sorting
    query = query.order(sortField, { ascending: sortDirection === 'asc' })

    // Get total count for pagination
    const { count } = await supabaseAdmin
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: products, error } = await query

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: 500 }
      )
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    return NextResponse.json({
      data: products,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasNext,
        hasPrev
      }
    })
  } catch (error) {
    console.error('Error in products API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      slug,
      description,
      short_description,
      price,
      compare_price,
      sku,
      category_id,
      brand,
      tags,
      is_featured,
      quantity
    } = body

    if (!name || !slug || !price) {
      return NextResponse.json(
        { error: 'Name, slug, and price are required' },
        { status: 400 }
      )
    }

    const { data: product, error } = await supabaseAdmin
      .from('products')
      .insert({
        name,
        slug,
        description,
        short_description,
        price,
        compare_price,
        sku,
        category_id,
        brand,
        tags,
        is_featured: is_featured || false,
        quantity: quantity || 0
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating product:', error)
      return NextResponse.json(
        { error: 'Failed to create product' },
        { status: 500 }
      )
    }

    return NextResponse.json({ data: product }, { status: 201 })
  } catch (error) {
    console.error('Error in products POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
