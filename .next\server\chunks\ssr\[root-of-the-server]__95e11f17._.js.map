{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { MagnifyingGlassIcon, ShoppingBagIcon, Bars3Icon, XMarkIcon, UserIcon } from '@heroicons/react/24/outline'\nimport { Category } from '@/types/database'\nimport { cn } from '@/lib/utils'\n\ninterface HeaderProps {\n  categories?: Category[]\n}\n\nexport default function Header({ categories = [] }: HeaderProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n  const [cartItemCount, setCartItemCount] = useState(0)\n  const router = useRouter()\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMenuOpen(false)\n  }, [])\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)\n      setSearchQuery('')\n    }\n  }\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      {/* Top bar */}\n      <div className=\"border-b bg-muted/50\">\n        <div className=\"container mx-auto px-4 py-2\">\n          <div className=\"flex items-center justify-between text-sm text-muted-foreground\">\n            <div className=\"hidden md:block\">\n              Free shipping on orders over $50\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link href=\"/help\" className=\"hover:text-foreground transition-colors\">\n                Help\n              </Link>\n              <Link href=\"/contact\" className=\"hover:text-foreground transition-colors\">\n                Contact\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main header */}\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-primary\">\n              SachMart\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            <Link href=\"/\" className=\"text-foreground hover:text-primary transition-colors\">\n              Home\n            </Link>\n            {categories.slice(0, 5).map((category) => (\n              <Link\n                key={category.id}\n                href={`/category/${category.slug}`}\n                className=\"text-foreground hover:text-primary transition-colors\"\n              >\n                {category.name}\n              </Link>\n            ))}\n            <Link href=\"/categories\" className=\"text-foreground hover:text-primary transition-colors\">\n              All Categories\n            </Link>\n          </nav>\n\n          {/* Search Bar */}\n          <div className=\"hidden md:block flex-1 max-w-md mx-8\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-2 pl-10 pr-4 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <button\n                type=\"submit\"\n                className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-2 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors\"\n              >\n                Search\n              </button>\n            </form>\n          </div>\n\n          {/* Right side icons */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Mobile search icon */}\n            <button className=\"md:hidden p-2 hover:bg-accent rounded-md transition-colors\">\n              <MagnifyingGlassIcon className=\"h-5 w-5\" />\n            </button>\n\n            {/* User account */}\n            <Link href=\"/account\" className=\"p-2 hover:bg-accent rounded-md transition-colors\">\n              <UserIcon className=\"h-5 w-5\" />\n            </Link>\n\n            {/* Shopping cart */}\n            <Link href=\"/cart\" className=\"relative p-2 hover:bg-accent rounded-md transition-colors\">\n              <ShoppingBagIcon className=\"h-5 w-5\" />\n              {cartItemCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-destructive text-destructive-foreground text-xs rounded-full flex items-center justify-center\">\n                  {cartItemCount > 9 ? '9+' : cartItemCount}\n                </span>\n              )}\n            </Link>\n\n            {/* Mobile menu button */}\n            <button\n              onClick={toggleMenu}\n              className=\"lg:hidden p-2 hover:bg-accent rounded-md transition-colors\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-5 w-5\" />\n              ) : (\n                <Bars3Icon className=\"h-5 w-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile search bar */}\n        <div className=\"md:hidden mt-4\">\n          <form onSubmit={handleSearch} className=\"relative\">\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full px-4 py-2 pl-10 pr-16 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n            />\n            <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <button\n              type=\"submit\"\n              className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors\"\n            >\n              Search\n            </button>\n          </form>\n        </div>\n      </div>\n\n      {/* Mobile Navigation Menu */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden border-t bg-background\">\n          <nav className=\"container mx-auto px-4 py-4 space-y-4\">\n            <Link\n              href=\"/\"\n              className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n              onClick={() => setIsMenuOpen(false)}\n            >\n              Home\n            </Link>\n            {categories.map((category) => (\n              <Link\n                key={category.id}\n                href={`/category/${category.slug}`}\n                className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {category.name}\n              </Link>\n            ))}\n            <Link\n              href=\"/categories\"\n              className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n              onClick={() => setIsMenuOpen(false)}\n            >\n              All Categories\n            </Link>\n            <div className=\"border-t pt-4 mt-4\">\n              <Link\n                href=\"/account\"\n                className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                My Account\n              </Link>\n              <Link\n                href=\"/orders\"\n                className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                My Orders\n              </Link>\n              <Link\n                href=\"/help\"\n                className=\"block py-2 text-foreground hover:text-primary transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Help & Support\n              </Link>\n            </div>\n          </nav>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAae,SAAS,OAAO,EAAE,aAAa,EAAE,EAAe;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,SAAS,IAAA,+IAAS;IAExB,uCAAuC;IACvC,IAAA,kNAAS,EAAC;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;YACjE,eAAe;QACjB;IACF;IAEA,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAkB;;;;;;0CAGjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAA0C;;;;;;kDAGvE,8OAAC,uKAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAM7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uKAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAuD;;;;;;oCAG/E,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC3B,8OAAC,uKAAI;4CAEH,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;4CAClC,WAAU;sDAET,SAAS,IAAI;2CAJT,SAAS,EAAE;;;;;kDAOpB,8OAAC,uKAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAuD;;;;;;;;;;;;0CAM5F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC,4PAAmB;4CAAC,WAAU;;;;;;sDAC/B,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,4PAAmB;4CAAC,WAAU;;;;;;;;;;;kDAIjC,8OAAC,uKAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC9B,cAAA,8OAAC,2NAAQ;4CAAC,WAAU;;;;;;;;;;;kDAItB,8OAAC,uKAAI;wCAAC,MAAK;wCAAQ,WAAU;;0DAC3B,8OAAC,gPAAe;gDAAC,WAAU;;;;;;4CAC1B,gBAAgB,mBACf,8OAAC;gDAAK,WAAU;0DACb,gBAAgB,IAAI,OAAO;;;;;;;;;;;;kDAMlC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAET,2BACC,8OAAC,8NAAS;4CAAC,WAAU;;;;;iEAErB,8OAAC,8NAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO7B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC,4PAAmB;oCAAC,WAAU;;;;;;8CAC/B,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAQN,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,uKAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;wBAGA,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,uKAAI;gCAEH,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;gCAClC,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,SAAS,IAAI;+BALT,SAAS,EAAE;;;;;sCAQpB,8OAAC,uKAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}