import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "FreshMart - Premium Organic Fruits & Vegetables Delivery",
  description: "Farm-fresh organic produce delivered to your doorstep. 100% organic fruits, vegetables, and fresh groceries with same-day delivery. Sustainably sourced, locally grown.",
  keywords: "organic fruits, fresh vegetables, organic grocery delivery, farm fresh produce, organic food store, healthy eating, sustainable farming, local produce",
  authors: [{ name: "FreshMart Team" }],
  creator: "FreshMart",
  publisher: "FreshMart",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: "FreshMart - Premium Organic Fruits & Vegetables Delivery",
    description: "Farm-fresh organic produce delivered to your doorstep. 100% organic, sustainably sourced, locally grown fruits and vegetables.",
    url: "/",
    siteName: "FreshMart",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "FreshMart - Premium Organic Fruits & Vegetables",
    description: "Farm-fresh organic produce delivered to your doorstep. 100% organic, sustainably sourced.",
    creator: "@freshmart",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

async function getCategories() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/categories?includeChildren=true`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });

    if (!response.ok) {
      throw new Error('Failed to fetch categories');
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories = await getCategories();

  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="relative flex min-h-screen flex-col">
          <Header categories={categories} />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
