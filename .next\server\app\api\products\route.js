var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/products/route.js")
R.c("server/chunks/node_modules_@supabase_node-fetch_lib_index_d6dc7176.js")
R.c("server/chunks/node_modules_next_0a8ce617._.js")
R.c("server/chunks/node_modules_tr46_3e4df63f._.js")
R.c("server/chunks/node_modules_@supabase_auth-js_dist_module_0e404d3a._.js")
R.c("server/chunks/node_modules_a45a867b._.js")
R.c("server/chunks/[root-of-the-server]__5159b99f._.js")
R.m("[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/products/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/products/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
