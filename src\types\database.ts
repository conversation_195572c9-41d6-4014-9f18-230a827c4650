export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image_url?: string
  parent_id?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  children?: Category[]
  parent?: Category
}

export interface Product {
  id: string
  name: string
  slug: string
  description?: string
  short_description?: string
  price: number
  compare_price?: number
  cost_price?: number
  sku?: string
  barcode?: string
  track_quantity: boolean
  quantity: number
  allow_backorder: boolean
  weight?: number
  dimensions?: {
    length?: number
    width?: number
    height?: number
  }
  category_id?: string
  brand?: string
  tags?: string[]
  is_active: boolean
  is_featured: boolean
  seo_title?: string
  seo_description?: string
  created_at: string
  updated_at: string
  category?: Category
  images?: ProductImage[]
  variants?: ProductVariant[]
  specifications?: ProductSpecification[]
}

export interface ProductImage {
  id: string
  product_id: string
  url: string
  alt_text?: string
  sort_order: number
  is_primary: boolean
  created_at: string
}

export interface ProductVariant {
  id: string
  product_id: string
  name: string
  price?: number
  compare_price?: number
  sku?: string
  barcode?: string
  quantity: number
  weight?: number
  options?: Record<string, string> // e.g., { size: "L", color: "Red" }
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface ProductSpecification {
  id: string
  product_id: string
  name: string
  value: string
  sort_order: number
  created_at: string
}

export interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  phone?: string
  date_of_birth?: string
  avatar_url?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Address {
  id: string
  user_id: string
  type: 'billing' | 'shipping'
  first_name: string
  last_name: string
  company?: string
  address_line_1: string
  address_line_2?: string
  city: string
  state: string
  postal_code: string
  country: string
  phone?: string
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface CartItem {
  id: string
  user_id?: string
  session_id?: string
  product_id: string
  variant_id?: string
  quantity: number
  created_at: string
  updated_at: string
  product?: Product
  variant?: ProductVariant
}

export interface Order {
  id: string
  user_id?: string
  order_number: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  subtotal: number
  tax_amount: number
  shipping_amount: number
  discount_amount: number
  total_amount: number
  currency: string
  billing_address?: Address
  shipping_address?: Address
  notes?: string
  created_at: string
  updated_at: string
  items?: OrderItem[]
}

export interface OrderItem {
  id: string
  order_id: string
  product_id?: string
  variant_id?: string
  product_name: string
  variant_name?: string
  price: number
  quantity: number
  total: number
  created_at: string
}

// API Response types
export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Search and filter types
export interface ProductFilters {
  category?: string
  minPrice?: number
  maxPrice?: number
  brand?: string
  tags?: string[]
  inStock?: boolean
  featured?: boolean
  search?: string
}

export interface ProductSort {
  field: 'name' | 'price' | 'created_at' | 'updated_at'
  direction: 'asc' | 'desc'
}

// Cart types
export interface CartSummary {
  items: CartItem[]
  itemCount: number
  subtotal: number
  total: number
}

// Form types
export interface AddToCartData {
  productId: string
  variantId?: string
  quantity: number
}

export interface UpdateCartItemData {
  id: string
  quantity: number
}

export interface CheckoutData {
  billingAddress: Omit<Address, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  shippingAddress: Omit<Address, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  sameAsShipping: boolean
  notes?: string
}
