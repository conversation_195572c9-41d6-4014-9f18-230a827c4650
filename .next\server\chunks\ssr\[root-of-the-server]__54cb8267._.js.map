{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b2991b2.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_b2991b2-module__9mH_6q__className\",\n  \"variable\": \"inter_b2991b2-module__9mH_6q__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_b2991b2.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,+JAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/layout/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/layout/Header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\nimport { EnvelopeIcon, PhoneIcon, MapPinIcon } from '@heroicons/react/24/outline'\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-muted/50 border-t\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 gradient-fresh rounded-full flex items-center justify-center\">\n                <span className=\"text-white text-lg\">🍎</span>\n              </div>\n              <h3 className=\"text-lg font-bold text-foreground\">FreshMart</h3>\n            </div>\n            <p className=\"text-muted-foreground text-sm leading-relaxed\">\n              Your trusted source for premium organic fruits and vegetables.\n              Farm-fresh produce delivered daily with a commitment to sustainability and your health.\n              <span className=\"block mt-2 flex items-center space-x-1\">\n                <span>🌱</span>\n                <span className=\"font-medium text-green-600\">100% Organic • Locally Sourced</span>\n              </span>\n            </p>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-muted-foreground hover:text-primary transition-colors\"\n              >\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-muted-foreground hover:text-primary transition-colors\"\n              >\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-muted-foreground hover:text-primary transition-colors\"\n              >\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323C6.001 8.198 7.152 7.708 8.449 7.708s2.448.49 3.323 1.416c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323c-.875.807-2.026 1.218-3.323 1.218zm7.718-1.297c-.875.875-2.026 1.365-3.323 1.365s-2.448-.49-3.323-1.365c-.875-.875-1.365-2.026-1.365-3.323s.49-2.448 1.365-3.323c.875-.875 2.026-1.365 3.323-1.365s2.448.49 3.323 1.365c.875.875 1.365 2.026 1.365 3.323s-.49 2.448-1.365 3.323z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-foreground flex items-center space-x-2\">\n              <span>🔗</span>\n              <span>Quick Links</span>\n            </h3>\n            <ul className=\"space-y-3 text-sm\">\n              <li>\n                <Link href=\"/about\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>🌱</span>\n                  <span>Our Farm Story</span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>📞</span>\n                  <span>Contact Us</span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/delivery\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>🚚</span>\n                  <span>Delivery Areas</span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/freshness\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>💚</span>\n                  <span>Freshness Guarantee</span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/organic\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>🏆</span>\n                  <span>Organic Certification</span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/recipes\" className=\"text-muted-foreground hover:text-primary transition-colors flex items-center space-x-2\">\n                  <span>👨‍🍳</span>\n                  <span>Fresh Recipes</span>\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Customer Service */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Customer Service</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/account\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  My Account\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/orders\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Order Tracking\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/wishlist\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Wishlist\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/help\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Help Center\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/support\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                  Live Chat\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Contact Info</h3>\n            <div className=\"space-y-3 text-sm\">\n              <div className=\"flex items-start space-x-3\">\n                <MapPinIcon className=\"h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0\" />\n                <div className=\"text-muted-foreground\">\n                  <p>123 Commerce Street</p>\n                  <p>Business District</p>\n                  <p>City, State 12345</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <PhoneIcon className=\"h-5 w-5 text-muted-foreground flex-shrink-0\" />\n                <a\n                  href=\"tel:+1234567890\"\n                  className=\"text-muted-foreground hover:text-primary transition-colors\"\n                >\n                  +****************\n                </a>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <EnvelopeIcon className=\"h-5 w-5 text-muted-foreground flex-shrink-0\" />\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-muted-foreground hover:text-primary transition-colors\"\n                >\n                  <EMAIL>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"mt-12 pt-8 border-t\">\n          <div className=\"max-w-md mx-auto text-center\">\n            <h3 className=\"text-lg font-semibold text-foreground mb-2\">\n              Subscribe to Our Newsletter\n            </h3>\n            <p className=\"text-muted-foreground text-sm mb-4\">\n              Get the latest updates on new products and exclusive offers!\n            </p>\n            <form className=\"flex space-x-2\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-3 py-2 border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-sm\"\n              />\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm font-medium\"\n              >\n                Subscribe\n              </button>\n            </form>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"mt-12 pt-8 border-t flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n          <div className=\"text-sm text-muted-foreground\">\n            © 2024 SachMart. All rights reserved.\n          </div>\n          <div className=\"flex space-x-6 text-sm\">\n            <Link href=\"/privacy\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n              Terms of Service\n            </Link>\n            <Link href=\"/cookies\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n              Cookie Policy\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAE,WAAU;;wCAAgD;sDAG3D,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAS,WAAU;;kEAC5B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;;kEAC9B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAY,WAAU;;kEAC/B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;;kEAC9B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;;kEAC9B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;sDAI/F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA6D;;;;;;;;;;;sDAI9F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA6D;;;;;;;;;;;sDAIhG,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAA6D;;;;;;;;;;;sDAI5F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;;;;;;;;;;;;;sCAQnG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iOAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;;;;;;;;;;;;;sDAGP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,8NAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;sDAIH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uOAAY;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;0CAG3D,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;0CAGlD,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgC;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA6D;;;;;;8CAG7F,8OAAC,uKAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA6D;;;;;;8CAG3F,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzG", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-sans\",\n});\n\nexport const metadata: Metadata = {\n  title: \"FreshMart - Premium Organic Fruits & Vegetables Delivery\",\n  description: \"Farm-fresh organic produce delivered to your doorstep. 100% organic fruits, vegetables, and fresh groceries with same-day delivery. Sustainably sourced, locally grown.\",\n  keywords: \"organic fruits, fresh vegetables, organic grocery delivery, farm fresh produce, organic food store, healthy eating, sustainable farming, local produce\",\n  authors: [{ name: \"FreshMart Team\" }],\n  creator: \"FreshMart\",\n  publisher: \"FreshMart\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),\n  openGraph: {\n    title: \"FreshMart - Premium Organic Fruits & Vegetables Delivery\",\n    description: \"Farm-fresh organic produce delivered to your doorstep. 100% organic, sustainably sourced, locally grown fruits and vegetables.\",\n    url: \"/\",\n    siteName: \"FreshMart\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"FreshMart - Premium Organic Fruits & Vegetables\",\n    description: \"Farm-fresh organic produce delivered to your doorstep. 100% organic, sustainably sourced.\",\n    creator: \"@freshmart\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n};\n\nasync function getCategories() {\n  try {\n    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/categories?includeChildren=true`, {\n      next: { revalidate: 3600 } // Revalidate every hour\n    });\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch categories');\n    }\n\n    const result = await response.json();\n    return result.data || [];\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    return [];\n  }\n}\n\nexport default async function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  const categories = await getCategories();\n\n  return (\n    <html lang=\"en\" className={inter.variable}>\n      <body className=\"min-h-screen bg-background font-sans antialiased\">\n        <div className=\"relative flex min-h-screen flex-col\">\n          <Header categories={categories} />\n          <main className=\"flex-1\">\n            {children}\n          </main>\n          <Footer />\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA;AACA;;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAiB;KAAE;IACrC,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI,6DAAmC;IACzD,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEA,eAAe;IACb,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,oCAAoC,CAAC,EAAE;YAChI,MAAM;gBAAE,YAAY;YAAK,EAAE,wBAAwB;QACrD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAEe,eAAe,WAAW,EACvC,QAAQ,EAGR;IACA,MAAM,aAAa,MAAM;IAEzB,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,mJAAK,CAAC,QAAQ;kBACvC,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iJAAM;wBAAC,YAAY;;;;;;kCACpB,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,iJAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/next/dist/client/app-dir/link.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/next/dist/client/app-dir/link.js/__nextjs-internal-proxy.cjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\nimport {\n  FetchStrategy,\n  type PrefetchTaskFetchStrategy,\n} from '../components/segment-cache'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null | 'unstable_forceStale'\n\n  /**\n   * (unstable) Switch to a full prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when type generation runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  if (onNavigate) {\n    let isDefaultPrevented = false\n\n    onNavigate({\n      preventDefault: () => {\n        isDefaultPrevented = true\n      },\n    })\n\n    if (isDefaultPrevented) {\n      return\n    }\n  }\n\n  React.startTransition(() => {\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  })\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n\n  const fetchStrategy =\n    prefetchProp !== false\n      ? getFetchStrategyFromPrefetchProp(prefetchProp)\n      : // TODO: it makes no sense to assign a fetchStrategy when prefetching is disabled.\n        FetchStrategy.PPR\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto' &&\n          props[key] !== 'unstable_forceStale'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\" | \"unstable_forceStale\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          fetchStrategy,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, fetchStrategy, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n\nfunction getFetchStrategyFromPrefetchProp(\n  prefetchProp: Exclude<LinkProps['prefetch'], undefined | false>\n): PrefetchTaskFetchStrategy {\n  if (\n    process.env.__NEXT_CACHE_COMPONENTS &&\n    process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ) {\n    // In the new implementation:\n    // - `prefetch={true}` is a runtime prefetch\n    //   (includes cached IO + params + cookies, with dynamic holes for uncached IO).\n    // - `unstable_forceStale` is a \"full\" prefetch\n    //   (forces inclusion of all dynamic data, i.e. the old behavior of `prefetch={true}`)\n    if (prefetchProp === true) {\n      return FetchStrategy.PPRRuntime\n    }\n    if (prefetchProp === 'unstable_forceStale') {\n      return FetchStrategy.Full\n    }\n\n    // `null` or `\"auto\"`: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport.\n    // This will also include invalid prop values that don't match the types specified here.\n    // (although those should've been filtered out by prop validation in dev)\n    prefetchProp satisfies null | 'auto'\n    // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n    // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n    return FetchStrategy.PPR\n  } else {\n    return prefetchProp === null || prefetchProp === 'auto'\n      ? // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n        // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n        FetchStrategy.PPR\n      : // In the old implementation without runtime prefetches, `prefetch={true}` forces all dynamic data to be prefetched.\n        // To preserve backwards-compatibility, anything other than `false`, `null`, or `\"auto\"` results in a full prefetch.\n        // (although invalid values should've been filtered out by prop validation in dev)\n        FetchStrategy.Full\n  }\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "isDefaultPrevented", "React", "startTransition", "dispatchNavigateAction", "current", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "fetchStrategy", "getFetchStrategyFromPrefetchProp", "FetchStrategy", "PPR", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext", "__NEXT_CACHE_COMPONENTS", "__NEXT_CLIENT_SEGMENT_CACHE", "PPRRuntime", "Full"], "mappings": "", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/%40heroicons/react/24/outline/esm/EnvelopeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,sNAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,sNAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,sNAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,mNAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/%40heroicons/react/24/outline/esm/PhoneIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PhoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PhoneIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,sNAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,sNAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,sNAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,mNAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1060, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Sources/Sach/Sach.Mart/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,sNAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,sNAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,sNAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,sNAAmB,CAAC,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,mNAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}